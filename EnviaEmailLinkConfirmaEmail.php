<?php

include_once "./logica/elementos/sendmail.php";
include_once "./bd/bd.php";
include_once "./bd/sql.php";

class EnviaEmailLinkConfirmaEmail
{
	private $Associado, $bd, $sql;
	public $titulo      = "<b>CONFIRMA��O DE E-MAIL</b><br />";
	public $acaoDestino = 'confirmaEmail';

	public function __construct($Associado)
	{
		$this->bd        = new BD();
		$this->sql       = new SQL();
		$this->Associado = $Associado;
	}

	public function enviaEmail($origem = null)
	{
		$dados = base64_encode(formata_cartao_tela($this->Associado->ultimoCartao) . '^' . date('Y-m-d'));
		$email = strtolower($this->Associado->asslogema);
		$assunto = _TBZ_NOME_PADRAO_;
		$msg = $this->titulo;
		$msg .= _TBZ_NOME_PADRAO_ASS_ . ": " . $this->Associado->nome . "<br />\n";

		// Ajustar mensagem baseada na origem
		if ($origem == 'APP') {
			$msg .= "Clique no link abaixo para confirmar seu e-mail no aplicativo.<br />";
		} else {
			$msg .= "Clique no link abaixo ou copie e cole no seu navegador.<br />";
		}

		// Garantir que sempre tenha origem definida
		if (empty($origem)) {
			$origem = 'WEB'; // Padrão para web se não especificado
		}

		$link = _HOST_ . "/tecbiz/tecbiz.php?a=657df2&modo=" . base64_encode($this->acaoDestino) . "&dados=$dados&origem=$origem";
		$msg .=  "<a href=\"$link\">$link</a><br><br>";
		$msg .= "<b>Data/Hora: </b>" . date("d/m/Y H:i:s") . "<br />\n";

		// Adicionar informação sobre origem para debug
		if ($origem == 'APP') {
			$msg .= "Sempre vai vir origem=APP<br />\n";
		}

		if (trim($email))
			new sendMail($email, $assunto, $msg, null, _TBZ_EMAIL_ATENDIMENTO_);

		$this->bd->consulta("update car100 set assemaenv = '{$msg}' where entcodent = {$this->Associado->entidade->codigo} and asscodass = {$this->Associado->codigo};");
	}
}
