import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, StatusBar, Image, BackHandler, Alert, FlatList } from 'react-native';
import { useAppContext } from '../context/AppContext';
import { MesData } from '../services/api';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import Cores from '../constants/Cores';
import EstilosComuns from '../constants/Estilos';
import {
  getResponsiveTextSize,
  getResponsivePadding,
  isSmallScreen,
  isMediumSmallScreen,
  isProblematicResolution,
  getProblematicResolutionAdjustments
} from '../utils/responsive';

interface SaldoScreenProps {
  navigation: {
    navigate: (screen: 'Login' | 'Home' | 'Saldo') => void;
  };
}

interface SaldoItem {
  mes: string;
  valor: string;
}

export default function SaldoScreen({ navigation }: SaldoScreenProps) {
  const { getMesesData, getUsuarioData } = useAppContext();
  const [saldoData, setSaldoData] = useState<SaldoItem[]>([]);
  const [totalMeses, setTotalMeses] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);

  // Responsividade melhorada
  const isSmall = isSmallScreen();
  const isMediumSmall = isMediumSmallScreen();
  const isProblematic = isProblematicResolution();
  const adjustments = getProblematicResolutionAdjustments();

  // Controlar o botão de voltar
  useEffect(() => {
    const backAction = () => {
      navigation.navigate('Home');
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [navigation]);

  // Carregar dados dos saldos
  useEffect(() => {
    loadSaldoData();
  }, []);

  const loadSaldoData = () => {
    setLoading(true);

    // Simular delay de carregamento
    setTimeout(() => {
      const mesesData = getMesesData();

      if (mesesData.length > 0) {
        const saldos: SaldoItem[] = mesesData.map(item => ({
          mes: item.mes.nome,
          valor: item.mes.valor,
        }));

        setSaldoData(saldos);
        setTotalMeses(saldos.length);
      }
      setLoading(false);
    }, 1000);
  };

  // Renderizar item da lista
  const renderSaldoItem = ({ item, index }: { item: SaldoItem; index: number }) => (
    <View style={[
      styles.saldoItem,
      index % 2 === 0 ? styles.evenRow : styles.oddRow,
      adjustments.flexAdjustments
    ]}>
      <Text style={[
        styles.mesText,
        { fontSize: getResponsiveTextSize(isSmall ? 14 : isMediumSmall ? 15 : 16) + (adjustments.fontSize || 0) }
      ]}>
        {item.mes}
      </Text>
      <Text style={[
        styles.valorText,
        { fontSize: getResponsiveTextSize(isSmall ? 14 : isMediumSmall ? 15 : 16) + (adjustments.fontSize || 0) }
      ]}>
        R$ {item.valor}
      </Text>
    </View>
  );

  // Se não há dados, mostrar mensagem
  if (saldoData.length === 0) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Saldo por meses</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.errorContainer}>
          <View style={styles.loadingContainer}>
            <LoadingSpinner message="Carregando saldos..." size="large" />
          </View>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.buttonText}>Voltar</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Se está carregando, mostrar loading
  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Saldo por meses</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.loadingContainer}>
          <LoadingSpinner message="Carregando saldos..." size="large" />
        </View>

        {/* Footer - Saldo não está mais no footer, então não mostra ativo */}
        <Footer currentScreen="Home" navigation={navigation} />
      </View>
    );
  }

  // Se não há dados, mostrar mensagem
  if (saldoData.length === 0) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Saldo por meses</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Nenhum dado de saldo encontrado</Text>
        </View>

        {/* Footer - Saldo não está mais no footer, então não mostra ativo */}
        <Footer currentScreen="Home" navigation={navigation} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate('Home')}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Saldo por meses</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Lista de Saldos */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.listContainer}>
          <FlatList
            data={saldoData}
            renderItem={renderSaldoItem}
            keyExtractor={(item, index) => `${item.mes}-${index}`}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>

        {/* Footer com informação */}
        <View style={[styles.footerInfo, adjustments.flexAdjustments]}>
          <Text style={[
            styles.footerText,
            { fontSize: getResponsiveTextSize(isSmall ? 12 : isMediumSmall ? 13 : 14) + (adjustments.fontSize || 0) }
          ]}>
            Serão exibidos {totalMeses} meses de parcelamento.
          </Text>
        </View>
      </ScrollView>

      {/* Footer - Saldo não está mais no footer, então não mostra ativo */}
      <Footer currentScreen="Home" navigation={navigation} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: 30,
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  listContainer: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  saldoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  evenRow: {
    backgroundColor: Cores.linhaPar,
  },
  oddRow: {
    backgroundColor: Cores.linhaImpar,
  },
  mesText: {
    fontSize: 16,
    color: Cores.textoEscuro,
    fontWeight: '500',
  },
  valorText: {
    fontSize: 16,
    color: Cores.primaria,
    fontWeight: 'bold',
  },
  separator: {
    height: 1,
    backgroundColor: Cores.bordaClara,
  },
  footerInfo: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    padding: 15,
    marginTop: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  footerText: {
    fontSize: 14,
    color: Cores.textoMedio,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: Cores.textoEscuro,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: Cores.primaria,
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  buttonText: {
    color: Cores.textoBranco,
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
