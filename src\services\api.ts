// Configuração centralizada de hosts
import { getCurrentHost, buildUrl, buildUrlEsqueceuSenha, isTestMode } from '../config/hosts';
import { compatibleFetch, getTecBizNetworkConfig, isAndroid8OrLower } from '../utils/networkConfig';

// Timeout para requisições (60 segundos, ou mais para Android 8.0)
const REQUEST_TIMEOUT = isAndroid8OrLower() ? 90000 : 60000;

// Função helper para fazer requisições com timeout e compatibilidade Android 8.0
const fetchWithTimeout = async (url: string, options: RequestInit = {}): Promise<Response> => {
  // Usar fetch compatível para Android 8.0
  if (isAndroid8OrLower()) {
    console.log('📱 Usando fetch compatível com Android 8.0');
    return compatibleFetch(url, {
      ...getTecBizNetworkConfig(),
      ...options,
    });
  }

  // Comportamento padrão para versões mais recentes
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('TIMEOUT');
    }
    throw error;
  }
};

// Interfaces baseadas na resposta da API PHP
export interface MesData {
  mes: {
    nome: string;
    valor: string;
  };
}

export interface SinteticoData {
  sintetico: {
    limite_mensal: string;
    saldo_mensal: string;
    limite_total: string;
    saldo_total: string;
  };
}

export interface UsuarioData {
  usuario: {
    codass: string;
    situacao: string;
    portador: string;
    etd: string;
    codent: string;
    senhaPadrao: boolean;
    carass: string;
    sitcar: string;
    mes_atual: string;
    maxParcelas: number;
    emailCadastro: string;
    cartaoFormatado: string;
    matricula: string;
    melhorDiaDeCompra: string;
    tokenid: string;
  };
}

export interface ErrorResponse {
  erro: string;
  mensagem_erro: string;
}

export type ApiResponse = (MesData | SinteticoData | UsuarioData)[] | ErrorResponse[];

export interface LoginSuccessResponse {
  success: true;
  data: ApiResponse;
  message?: string; // Mensagem opcional para casos de sucesso
}

export interface LoginErrorResponse {
  success: false;
  message: string;
  isTimeout?: boolean;
}

export type LoginResponse = LoginSuccessResponse | LoginErrorResponse;

// Função para formatar cartão (remover pontos)
function removeCardFormatting(card: string): string {
  return card.replace(/\./g, '');
}

// Função para detectar se é email ou cartão
function isEmail(input: string): boolean {
  return input.includes('@');
}

export async function login(cardOrEmail: string, password: string, pushToken?: string): Promise<LoginResponse> {
  try {
    // Remove formatação do cartão se não for email
    const cleanCardOrEmail = isEmail(cardOrEmail) ? cardOrEmail : removeCardFormatting(cardOrEmail);

    // Obter token push para enviar junto com o login
    let finalPushToken = '';

    // Prioridade 1: Token passado como parâmetro
    if (pushToken && pushToken.length > 20) {
      finalPushToken = pushToken;
      console.log('✅ Usando token passado como parâmetro:', finalPushToken.substring(0, 50) + '...');
    } else {
      // Prioridade 2: Token do storage (fallback)
      try {
        const { robustStorage } = await import('../utils/robustStorage');
        const storedToken = await robustStorage.getItem('push_token');

        console.log('🔍 Debug token storage (fallback):');
        console.log('   - Token encontrado:', storedToken ? 'SIM' : 'NÃO');
        console.log('   - Tamanho:', storedToken ? storedToken.length : 0);

        if (storedToken && storedToken.length > 20) {
          finalPushToken = storedToken;
          console.log('✅ Token obtido do storage:', finalPushToken.substring(0, 50) + '...');
        } else {
          console.log('⚠️ Nenhum token válido encontrado');
        }
      } catch (error) {
        console.warn('⚠️ Erro ao obter token do storage:', error);
      }
    }

    console.log('🎯 Token final para login:', finalPushToken ? finalPushToken.substring(0, 50) + '...' : 'NENHUM');

    // Constrói a URL da requisição usando configuração centralizada
    const params = new URLSearchParams({
      a: '2547a0',
      senass: password,
    });

    // Adiciona parâmetro correto baseado no tipo de input
    if (isEmail(cleanCardOrEmail)) {
      params.append('emaass', cleanCardOrEmail);
    } else {
      params.append('carass', cleanCardOrEmail);
    }

    // Adicionar token push se disponível (parâmetro tokenid para o PHP)
    if (finalPushToken) {
      params.append('tokenid', finalPushToken);
      console.log('✅ Adicionando tokenid aos parâmetros:', finalPushToken.substring(0, 50) + '...');
    } else {
      console.log('❌ Nenhum token disponível - login sem tokenid');
    }

    const url = buildUrl(params.toString());

    console.log('🔗 URL FINAL da requisição:', url);
    console.log('📧 Email/Cartão:', cleanCardOrEmail);
    console.log('🔒 Senha:', password ? '***' : 'vazia');
    console.log('🎯 Parâmetros enviados:', {
      a: '2547a0',
      [isEmail(cleanCardOrEmail) ? 'emaass' : 'carass']: cleanCardOrEmail,
      senass: '***',
      tokenid: finalPushToken ? finalPushToken.substring(0, 30) + '...' : 'NÃO ENVIADO'
    });

    // Faz a requisição com timeout usando função compatível com Android 8.0
    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'TecBizApp/1.0',
      },
    });

    console.log('📡 Status da resposta:', response.status);
    console.log('📡 Headers da resposta:', response.headers);

    if (!response.ok) {
      console.error('❌ Erro HTTP:', response.status, response.statusText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();
    console.log('📦 Dados recebidos:', JSON.stringify(data, null, 2));

    // Verifica se a resposta contém erro
    if (Array.isArray(data) && data.length > 0) {
      const firstItem = data[0] as any;
      if (firstItem.erro) {
        console.log('⚠️ Erro da API:', firstItem.mensagem_erro);
        return {
          success: false,
          message: firstItem.mensagem_erro || 'Erro desconhecido',
        };
      }
    }

    // Se chegou até aqui, o login foi bem-sucedido
    console.log('✅ Login bem-sucedido!');

    // Log do token enviado (se houver)
    if (finalPushToken) {
      console.log('✅ Token enviado junto com login para o PHP processar');
      console.log('🎯 Token:', finalPushToken.substring(0, 50) + '...');
    }

    return {
      success: true,
      data: data,
    };

  } catch (error) {
    console.error('❌ Erro na requisição:', error);
    console.error('❌ Tipo do erro:', typeof error);
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'N/A');

    // Verificar se é timeout
    if (error instanceof Error && error.message === 'TIMEOUT') {
      return {
        success: false,
        message: 'TIMEOUT',
        isTimeout: true,
      };
    }

    return {
      success: false,
      message: 'Falha ao conectar ao servidor. Verifique sua conexão.',
    };
  }
}

// Tipos para o Extrato
export interface ExtratoItem {
  dataCompleta: string;
  parcelas: string;
  nomeEstabelecimento: string;
  valorParcela: string;
  data: string;
  loja: string;
  valor: string;
  estornada?: number; // Campo para identificar se o item foi estornado (0 = não estornado, 1 = estornado)
}

export interface ExtratoResponse {
  mesAtual: string;
  mes: string;
  ano: string;
  mesSelecionado: string;
  extrato: ExtratoItem[];
  ultimoConsumo: {
    mesUniversal: number;
    mes: string;
    ano: string;
  };
  mesUniversalAtual: number;
  total: string;
}

export interface ExtratoApiResponse {
  success: boolean;
  message: string;
  data: ExtratoResponse | null;
}

// Função para buscar extrato
export const getExtrato = async (codass: string, codent: string, mesform?: string, anoform?: string): Promise<ExtratoApiResponse> => {
  try {
    const params = new URLSearchParams({
      a: '751fc0',
      codass: codass,
      codent: codent,
    });

    if (mesform && anoform) {
      params.append('mesform', mesform);
      params.append('anoform', anoform);
    }

    const url = buildUrl(params.toString());

    console.log('🔗 Fazendo requisição de extrato para:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status da resposta do extrato:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ExtratoResponse = await response.json();
    console.log('📦 Dados do extrato recebidos:', data);

    return {
      success: true,
      message: 'Extrato carregado com sucesso!',
      data: data,
    };

  } catch (error) {
    console.error('❌ Erro ao buscar extrato:', error);
    return {
      success: false,
      message: 'Erro ao carregar extrato. Verifique sua conexão.',
      data: null,
    };
  }
};

// Interface para autorizações pendentes
export interface AutorizacaoItem {
  autnumaut: string;
  data_compra_formatada: string;
  valor_formatado: string;
  lojnomfan: string;
  autnumpar: string;
  autvalope: string;
  autvalpri: string;
  autdatmov: string;
  authorini: string;
}

export interface AutorizacoesPendentesData {
  extrato: AutorizacaoItem[];
}

export interface AutorizacoesApiResponse {
  success: boolean;
  message: string;
  data: AutorizacoesPendentesData | null;
}

// Função para buscar autorizações pendentes
export const getAutorizacoesPendentes = async (codass: string, codent: string): Promise<AutorizacoesApiResponse> => {
  try {
    const params = new URLSearchParams({
      a: 'eac833',
      codass: codass,
      codent: codent,
    });

    const url = buildUrl(params.toString());
    console.log('🔗 Fazendo requisição para autorizações:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status da resposta autorizações:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Dados de autorizações recebidos:', data);

    // Se data é um array vazio, não há autorizações
    if (Array.isArray(data) && data.length === 0) {
      console.log('ℹ️ Nenhuma autorização pendente encontrada');
      return {
        success: true,
        message: 'Nenhuma autorização pendente',
        data: { extrato: [] },
      };
    }

    // Se há dados, processar
    return {
      success: true,
      message: 'Autorizações carregadas com sucesso!',
      data: { extrato: data || [] },
    };

  } catch (error) {
    console.error('❌ Erro ao buscar autorizações:', error);
    return {
      success: false,
      message: 'Erro ao carregar autorizações. Verifique sua conexão.',
      data: null,
    };
  }
};

// Função para confirmar autorizações
export const confirmarAutorizacoes = async (
  codass: string,
  codent: string,
  autssim: string,
  autsnao: string,
  justificativa: string
): Promise<{ success: boolean; data?: any; message: string }> => {
  try {
    console.log('🔗 Confirmando autorizações:', { codass, codent, autssim, autsnao, justificativa });

    const params = `a=047c14&codent=${codent}&codass=${codass}&autssim=${autssim}&autsnao=${autsnao}&justificativa=${encodeURIComponent(justificativa)}`;
    const url = buildUrl(params);
    console.log('🔗 URL confirmação:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Status da resposta confirmação:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta confirmação:', data);

    // Verificar se a confirmação foi bem-sucedida (código 000)
    if (data.codigo === '000') {
      return {
        success: true,
        data,
        message: data.message || 'Autorizações confirmadas com sucesso!'
      };
    } else {
      return {
        success: false,
        data,
        message: data.message || 'Erro ao confirmar autorizações'
      };
    }

  } catch (error) {
    console.error('❌ Erro ao confirmar autorizações:', error);
    return {
      success: false,
      data: null,
      message: 'Erro ao confirmar autorizações'
    };
  }
};

// Função para esqueceu senha (servidor de produção)
// Função para testar conectividade
export const testarConectividade = async (): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('🔍 Testando conectividade...');

    // Testar URLs simples primeiro
    const testUrls = [
      'http://httpbin.org/get', // Serviço público para teste
      'http://191.252.178.89/tecbiz/tecbiz.php?a=test', // Servidor específico
    ];

    for (const testUrl of testUrls) {
      try {
        console.log(`🔗 Testando: ${testUrl}`);
        const response = await fetchWithTimeout(testUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

        console.log(`✅ Sucesso: ${testUrl} - Status: ${response.status}`);
        return { success: true, message: `Conectividade OK - ${testUrl}` };
      } catch (error) {
        console.log(`❌ Falhou: ${testUrl} - ${error}`);
      }
    }

    return { success: false, message: 'Todas as URLs de teste falharam' };
  } catch (error) {
    return { success: false, message: `Erro no teste: ${error}` };
  }
};

export const esqueceuSenha = async (
  cartaoEmail: string,
  cpf: string,
  selecionado: 'email' | 'cartao'
): Promise<{ success: boolean; data?: any; message: string }> => {
  try {
    console.log('🔗 Enviando esqueceu senha:', { cartaoEmail, cpf, selecionado });

    // Primeiro testar conectividade
    console.log('🔍 Testando conectividade antes da requisição...');
    const conectividade = await testarConectividade();
    console.log('📊 Resultado do teste:', conectividade);

    // Usar apenas HTTP (HTTPS tem problema de certificado)
    // Esqueceu senha sempre usa HOST_QUENTE (produção) para funcionar o email
    const params = `a=79c8bd&cartao_email=${encodeURIComponent(cartaoEmail)}&cpf=${cpf}&selecionado=${selecionado}`;
    const url = buildUrlEsqueceuSenha(params);

    console.log('🔗 URL esqueceu senha:', url);

    console.log('📡 Iniciando requisição HTTP...');
    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'TecBizApp/1.0',
        'Cache-Control': 'no-cache',
      },
    });

    console.log('📡 HTTP completado, processando resposta...');
    console.log('📡 Status da resposta:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta esqueceu senha:', data);

    // Tratar múltiplos formatos de resposta
    let responseData: any;

    if (Array.isArray(data) && data.length > 0) {
      // Formato array: [{"coderro": 12, "erro": "..."}]
      responseData = data[0];
    } else if (data && typeof data === 'object' && data.coderro !== undefined) {
      // Formato objeto direto: {"coderro": "12", "erro": "..."}
      responseData = data;
    } else if (data && typeof data === 'object' && data['0'] && data['0'].coderro !== undefined) {
      // Formato objeto com chave "0": {"0": {"coderro": 0, "erro": "ok"}, "13": {...}}
      responseData = data['0'];
      console.log('🔍 Formato com chave "0" detectado');
    } else {
      console.log('❌ Formato de resposta não reconhecido:', data);
      return {
        success: false,
        data,
        message: 'Resposta inesperada do servidor'
      };
    }

    // Verificar se há erro na resposta
    if (responseData && responseData.coderro !== undefined) {
      const coderro = parseInt(responseData.coderro.toString()); // Converter para número
      const erro = responseData.erro;
      const erroMobile = responseData.erroMobile; // Mensagem específica para mobile

      console.log('🔍 Código de erro:', coderro, 'Mensagem:', erro);

      if (coderro > 0) {
        // Erro - usar mensagem mobile(api)
        return {
          success: false,
          data,
          message: erroMobile || erro
        };
      } else {
        // Sucesso (coderro = 0)
        console.log('✅ Sucesso (coderro = 0) - seguindo lógica Cordova');

        // Extrair dados do usuário se disponível
        let userData = null;
        if (data['13'] && data['13'].usuario) {
          userData = data['13'].usuario;
          console.log('👤 Dados do usuário extraídos:', userData);
        }

        return {
          success: true,
          data: { ...data, userData, isEmailConfirmed: true },
          message: 'Email confirmado. Você pode alterar sua senha agora.'
        };
      }
    }

    return {
      success: false,
      data,
      message: 'Resposta inesperada do servidor'
    };

  } catch (error) {
    console.error('❌ Erro ao enviar esqueceu senha:', error);

    // Sugerir soluções para o problema de rede
    const networkError = error instanceof Error && error.message.includes('Network request failed');
    const message = networkError
      ? 'ERRO DE REDE: Possível bloqueio de firewall/proxy. Tente conectar de outra rede ou contate o administrador.'
      : 'Erro ao conectar com o servidor';

    return {
      success: false,
      data: null,
      message
    };
  }
};

// Função para verificar força da senha
export const verificarForcaSenha = async (
  senha: string
): Promise<{ success: boolean; data?: any; message: string }> => {
  try {
    console.log('🔗 Verificando força da senha');

    // Usar configuração centralizada para verificar senha
    const params = `a=e9a0e3&senha=${encodeURIComponent(senha)}`;
    const url = buildUrl(params);
    console.log('🔗 URL verificar força:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'TecBizApp/1.0',
      },
    });

    console.log('📡 Status da resposta força senha:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta força senha:', data);

    // Verificar se a resposta tem o formato esperado
    if (data && typeof data.erro !== 'undefined') {
      return {
        success: true,
        data: {
          erro: data.erro,
          avaliacao: data.avaliacao,
          complemento: data.complemento
        },
        message: data.avaliacao || 'Senha verificada'
      };
    }

    return {
      success: false,
      data,
      message: 'Formato de resposta inesperado'
    };

  } catch (error) {
    console.error('❌ Erro ao verificar força da senha:', error);
    return {
      success: false,
      data: null,
      message: 'Erro ao verificar senha'
    };
  }
};

// Função para alterar senha do associado
export const alterarSenhaAssociado = async (
  codass: string,
  codent: string,
  newsen: string,
  mail?: string,
  notification?: string,
  validateEmail?: boolean
): Promise<{ success: boolean; data?: any; message: string }> => {
  try {
    console.log('🔗 Alterando senha do associado:', { codass, codent, mail, notification, validateEmail });

    // Usar configuração centralizada para alterar senha
    let params = `a=05cadd&codass=${codass}&codent=${codent}&newsen=${encodeURIComponent(newsen)}`;

    // Adicionar parâmetros opcionais se fornecidos
    if (mail) {
      params += `&mail=${encodeURIComponent(mail)}`;
    }
    if (notification) {
      params += `&notification=${encodeURIComponent(notification)}`;
    }
    if (validateEmail !== undefined) {
      params += `&validateEmail=${validateEmail ? '1' : '0'}`;
    }

    const url = buildUrl(params);

    console.log('🔗 URL alterar senha:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Status da resposta alterar senha:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta alterar senha:', data);

    // Verificar se a alteração foi bem-sucedida
    if (data && data.retorno !== undefined) {
      if (data.retorno === 0) {
        // Sucesso
        return {
          success: true,
          data,
          message: data.descricao || 'Senha alterada com sucesso!'
        };
      } else {
        // Erro
        return {
          success: false,
          data,
          message: data.descricao || 'Erro ao alterar senha'
        };
      }
    }

    return {
      success: false,
      data,
      message: 'Resposta inesperada do servidor'
    };

  } catch (error) {
    console.error('❌ Erro ao alterar senha:', error);
    return {
      success: false,
      data: null,
      message: 'Erro ao conectar com o servidor'
    };
  }
};

// Função para alterar senha usando cartão (para deep links de email)
export const alterarSenhaPorCartao = async (
  cartao: string,
  newsen: string,
  modo?: string,
  dados?: string
): Promise<{ success: boolean; data?: any; message: string }> => {
  try {
    console.log('💳 Alterando senha por cartão:', { cartao: cartao.substring(0, 4) + '****', modo, dados });

    // Usar uma API específica que aceita cartão ao invés de codass/codent
    // Isso é útil para casos de deep link onde não temos os códigos
    let params = `a=05cadd&cartao=${encodeURIComponent(cartao)}&newsen=${encodeURIComponent(newsen)}`;

    // Adicionar dados de confirmação se disponíveis
    if (modo) {
      params += `&modo=${encodeURIComponent(modo)}`;
    }
    if (dados) {
      params += `&dados=${encodeURIComponent(dados)}`;
    }

    // Marcar como origem do app
    params += `&origem=APP`;

    const url = buildUrl(params);

    console.log('🔗 URL alterar senha por cartão:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Status da resposta alterar senha por cartão:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta alterar senha por cartão:', data);

    // Verificar se a alteração foi bem-sucedida
    if (data && data.retorno !== undefined) {
      if (data.retorno === 0) {
        // Sucesso
        return {
          success: true,
          data,
          message: data.descricao || 'Senha alterada com sucesso!'
        };
      } else {
        // Erro
        return {
          success: false,
          data,
          message: data.descricao || 'Erro ao alterar senha'
        };
      }
    }

    return {
      success: false,
      data,
      message: 'Resposta inesperada do servidor'
    };

  } catch (error) {
    console.error('❌ Erro ao alterar senha por cartão:', error);
    return {
      success: false,
      data: null,
      message: 'Erro ao conectar com o servidor'
    };
  }
};

// Interfaces para Rede Conveniada
export interface Estado {
  uf: string;
}

export interface Cidade {
  codigo: string;
  cidade: string;
}

export interface Segmento {
  codseg: string;
  desseg: string;
}

export interface Estabelecimento {
  lojcodloj: string;
  nome: string;
  cor: string;
  destaque: number;
  cod_segmento: string;
  segmento: string;
  razao: string;
  principalMeio: string;
}

export interface FilialEstabelecimento {
  endereco: string;
  fone: string;
  cidade: string;
  uf: string;
}

// Função para listar estados
export const listarEstados = async (codent: string): Promise<{ success: boolean; data?: Estado[]; message: string }> => {
  try {
    console.log('🌎 Listando estados para codent:', codent);

    const params = `a=a8d017&codent=${codent}&acs=1`;
    const url = buildUrl(params);
    console.log('🔗 URL listar estados:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status listar estados:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta listar estados:', data);

    return {
      success: true,
      data,
      message: 'Estados carregados com sucesso'
    };

  } catch (error) {
    console.error('❌ Erro ao listar estados:', error);
    return {
      success: false,
      data: [],
      message: 'Erro ao carregar estados'
    };
  }
};

// Função para listar cidades
export const listarCidades = async (codent: string, uf?: string): Promise<{ success: boolean; data?: Cidade[]; message: string }> => {
  try {
    console.log('🏙️ Listando cidades para codent:', codent, 'uf:', uf);

    let params = `a=a8d017&codent=${codent}&acs=2`;
    if (uf && uf !== '0') {
      params += `&uf=${uf}`;
    }

    const url = buildUrl(params);
    console.log('🔗 URL listar cidades:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status listar cidades:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta listar cidades:', data);

    return {
      success: true,
      data,
      message: 'Cidades carregadas com sucesso'
    };

  } catch (error) {
    console.error('❌ Erro ao listar cidades:', error);
    return {
      success: false,
      data: [],
      message: 'Erro ao carregar cidades'
    };
  }
};

// Função para listar segmentos
export const listarSegmentos = async (codent: string, uf?: string, cidade?: string): Promise<{ success: boolean; data?: Segmento[]; message: string }> => {
  try {
    console.log('🏢 Listando segmentos para codent:', codent, 'uf:', uf, 'cidade:', cidade);

    let params = `a=a8d017&codent=${codent}&acs=3`;
    if (uf && uf !== '0') {
      params += `&uf=${uf}`;
    }
    if (cidade && cidade !== '0') {
      params += `&cid=${cidade}`;
    }

    const url = buildUrl(params);
    console.log('🔗 URL listar segmentos:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status listar segmentos:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta listar segmentos:', data);

    return {
      success: true,
      data,
      message: 'Segmentos carregados com sucesso'
    };

  } catch (error) {
    console.error('❌ Erro ao listar segmentos:', error);
    return {
      success: false,
      data: [],
      message: 'Erro ao carregar segmentos'
    };
  }
};

// Função para listar estabelecimentos
export const listarEstabelecimentos = async (
  codent: string,
  uf?: string,
  cidade?: string,
  segmento?: string
): Promise<{ success: boolean; data?: Estabelecimento[]; message: string }> => {
  try {
    console.log('🏪 Listando estabelecimentos para:', { codent, uf, cidade, segmento });

    let params = `a=a8d017&codent=${codent}&acs=4`;
    if (uf && uf !== '0') {
      params += `&uf=${uf}`;
    }
    if (cidade && cidade !== '0') {
      params += `&cid=${cidade}`;
    }
    if (segmento && segmento !== '0') {
      params += `&seg=${segmento}`;
    }

    const url = buildUrl(params);
    console.log('🔗 URL listar estabelecimentos:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status listar estabelecimentos:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta listar estabelecimentos:', data);

    return {
      success: true,
      data,
      message: 'Estabelecimentos carregados com sucesso'
    };

  } catch (error) {
    console.error('❌ Erro ao listar estabelecimentos:', error);
    return {
      success: false,
      data: [],
      message: 'Erro ao carregar estabelecimentos'
    };
  }
};

// Interface para resposta do bloqueio de cartão
export interface BloquearCartaoResponse {
  retorno: string;
  descricao: string;
}

// Função para atualizar dados do usuário usando re-login
export const atualizarDadosUsuario = async (
  cartao: string,
  senha: string
): Promise<{ success: boolean; data?: ApiResponse; message: string }> => {
  try {
    console.log('🔄 Atualizando dados do usuário via re-login...');
    console.log('📋 Cartão:', cartao.substring(0, 4) + '****');

    // Usa a mesma função de login para buscar dados atualizados
    const loginResponse = await login(cartao, senha);

    if (loginResponse.success && loginResponse.data) {
      console.log('✅ Dados do usuário atualizados com sucesso via re-login');
      return {
        success: true,
        data: loginResponse.data,
        message: 'Dados atualizados com sucesso'
      };
    } else {
      console.log('❌ Falha no re-login:', loginResponse.message);
      return {
        success: false,
        message: loginResponse.message || 'Erro ao atualizar dados'
      };
    }
  } catch (error) {
    console.log('❌ Erro no re-login para atualização:', error);
    return {
      success: false,
      message: 'Erro de conexão. Verifique sua internet e tente novamente.'
    };
  }
};

// Função para bloquear cartão
export const bloquearCartao = async (
  senha: string,
  cartao: string,
  justificativa: string
): Promise<{ success: boolean; data?: BloquearCartaoResponse; message: string }> => {
  try {
    console.log('🔒 Bloqueando cartão...');
    console.log('📋 Dados:', { cartao: cartao.substring(0, 4) + '****', justificativa: justificativa.substring(0, 20) + '...' });

    const params = `a=c06dd5&senha=${encodeURIComponent(senha)}&cartao=${encodeURIComponent(cartao)}&justificativa=${encodeURIComponent(justificativa)}&origem=1`;
    const url = buildUrl(params);

    console.log('🔗 URL bloquear cartão:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📥 Resposta bruta bloquear cartão:', responseText);

    let responseData: BloquearCartaoResponse;
    try {
      responseData = JSON.parse(responseText);
    } catch (parseError) {
      console.log('❌ Erro ao fazer parse da resposta:', parseError);
      return { success: false, message: 'Erro ao processar resposta do servidor' };
    }

    if (responseData.retorno === '0') {
      console.log('✅ Cartão bloqueado com sucesso');
      return {
        success: true,
        data: responseData,
        message: 'Cartão bloqueado com sucesso!'
      };
    } else {
      console.log('❌ Erro ao bloquear cartão:', responseData.descricao);
      return {
        success: false,
        data: responseData,
        message: (responseData as any)["1"] || 'Erro ao bloquear cartão'
      };
    }
  } catch (error) {
    console.log('❌ Erro na requisição bloquear cartão:', error);
    return {
      success: false,
      message: 'Erro de conexão. Verifique sua internet e tente novamente.'
    };
  }
};

// Função para listar filiais de um estabelecimento
export const listarFiliaisEstabelecimento = async (
  ecs: string,
  uf?: string,
  cidade?: string
): Promise<{ success: boolean; data?: FilialEstabelecimento[]; message: string }> => {
  try {
    console.log('🏢 Listando filiais para ecs:', ecs, 'uf:', uf, 'cidade:', cidade);

    let params = `a=a8d017&acs=5&ecs=${ecs}`;
    if (uf && uf !== '0') {
      params += `&uf=${uf}`;
    }
    if (cidade && cidade !== '0') {
      params += `&cid=${cidade}`;
    }

    const url = buildUrl(params);
    console.log('🔗 URL listar filiais:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status listar filiais:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta listar filiais:', data);

    return {
      success: true,
      data,
      message: 'Filiais carregadas com sucesso'
    };

  } catch (error) {
    console.error('❌ Erro ao listar filiais:', error);
    return {
      success: false,
      data: [],
      message: 'Erro ao carregar filiais'
    };
  }
};

// =====================================================
// FUNÇÕES PARA NOTIFICAÇÕES PUSH
// =====================================================

// Interface para notificação
export interface NotificationItem {
  id: string;
  title: string;
  body: string;
  data: any;
  timestamp: number;
  read: boolean;
  codass?: string;
  codent?: string;
  cartao?: string;
}

export interface NotificationsResponse {
  success: boolean;
  count: number;
  unread_count: number;
  notifications: NotificationItem[];
  days: number;
  timestamp: number;
  message?: string;
}

// Função para buscar notificações do usuário
export const getNotifications = async (
  token: string,
  days: number = 7
): Promise<{ success: boolean; data?: NotificationsResponse; message: string }> => {
  try {
    console.log('📥 Buscando notificações para token:', token.substring(0, 30) + '...');

    const params = `a=9699b8&token=${encodeURIComponent(token)}&days=${days}`; // VOCÊ DEVE PREENCHER O 'a='
    const url = buildUrl(params);
    console.log('🔗 URL buscar notificações:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status buscar notificações:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta buscar notificações:', data);

    if (data.success) {
      return {
        success: true,
        data,
        message: `${data.count} notificações carregadas (${data.unread_count} não lidas)`
      };
    } else {
      return {
        success: false,
        message: data.error || 'Erro ao buscar notificações'
      };
    }

  } catch (error) {
    console.error('❌ Erro ao buscar notificações:', error);
    return {
      success: false,
      message: 'Erro ao conectar com o servidor'
    };
  }
};

// Função para marcar notificações como lidas
export const markNotificationsAsRead = async (
  token: string,
  notificationIds: string[]
): Promise<{ success: boolean; data?: any; message: string }> => {
  try {
    console.log('✅ Marcando notificações como lidas:', notificationIds);

    const params = `a=3e20d3&token=${encodeURIComponent(token)}&notification_ids=${notificationIds.join(',')}`; // VOCÊ DEVE PREENCHER O 'a='
    const url = buildUrl(params);
    console.log('🔗 URL marcar como lidas:', url);

    const response = await fetchWithTimeout(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Status marcar como lidas:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 Resposta marcar como lidas:', data);

    if (data.success) {
      return {
        success: true,
        data,
        message: `${data.updated_count} notificações marcadas como lidas`
      };
    } else {
      return {
        success: false,
        message: data.error || 'Erro ao marcar como lidas'
      };
    }

  } catch (error) {
    console.error('❌ Erro ao marcar como lidas:', error);
    return {
      success: false,
      message: 'Erro ao conectar com o servidor'
    };
  }
};