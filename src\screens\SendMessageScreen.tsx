import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Cores from '../constants/Cores';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import { NavigationProp } from '../types/navigation';

interface SendMessageScreenProps {
  navigation: NavigationProp;
}

const SendMessageScreen: React.FC<SendMessageScreenProps> = ({ navigation }) => {
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack());

  const handleSendMessage = async () => {
    if (!title.trim() || !message.trim()) {
      Alert.alert('Erro', 'Por favor, preencha o título e a mensagem');
      return;
    }

    setLoading(true);

    try {
      console.log('📤 Enviando mensagem push...');
      console.log('📋 Título:', title);
      console.log('📋 Mensagem:', message);

      // SIMULAÇÃO DE ENVIO LOCAL + NOTIFICAÇÃO REAL
      const pushNotificationService = (await import('../services/pushNotificationService')).default;

      // Criar notificação local para demonstração
      const testNotification = {
        id: 'local_' + Date.now(),
        title: title.trim(),
        body: message.trim(),
        data: {
          screen: 'Notifications',
          timestamp: Date.now(),
          sender: 'admin_test',
          priority: 'high'
        },
        timestamp: Date.now(),
        read: false,
        type: 'local' as const
      };

      // Adicionar notificação localmente
      await pushNotificationService.addNotificationPublic(testNotification);

      // Simular resposta com 1 dispositivo (local)
      const response = {
        ok: true,
        json: async () => ({
          success: true,
          message: 'Notificação enviada com sucesso',
          sent_count: 1, // Mostra 1 dispositivo (local)
          note: 'Mensagem adicionada localmente. Para push real, configure FCM no backend.'
        })
      };

      const result = await response.json();

      if (result.success) {
        Alert.alert(
          'Sucesso!',
          `Mensagem enviada para ${result.sent_count || 1} dispositivo(s)\n\n${result.note || ''}`,
          [
            {
              text: 'OK',
              onPress: () => {
                setTitle('');
                setMessage('');
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }

    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      Alert.alert(
        'Erro',
        'Não foi possível enviar a mensagem. Tente novamente.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSendTestMessage = async () => {
    setTitle('Mensagem de Teste');
    setMessage('Esta é uma mensagem de teste do sistema de notificações push do TecBiz. Se você recebeu esta mensagem, o sistema está funcionando corretamente!');
    
    // Aguardar um pouco para o usuário ver os campos preenchidos
    setTimeout(() => {
      handleSendMessage();
    }, 500);
  };

  return (
    <View style={styles.container}>
      <Header
        title="Enviar Mensagem"
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <Text style={styles.label}>Título da Mensagem</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Digite o título da mensagem..."
            placeholderTextColor={Cores.textoMedio}
            maxLength={100}
            editable={!loading}
          />
          <Text style={styles.charCount}>{title.length}/100</Text>

          <Text style={styles.label}>Mensagem</Text>
          <TextInput
            style={[styles.input, styles.messageInput]}
            value={message}
            onChangeText={setMessage}
            placeholder="Digite a mensagem que será enviada para todos os usuários..."
            placeholderTextColor={Cores.textoMedio}
            multiline
            numberOfLines={6}
            maxLength={500}
            textAlignVertical="top"
            editable={!loading}
          />
          <Text style={styles.charCount}>{message.length}/500</Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.testButton]}
              onPress={handleSendTestMessage}
              disabled={loading}
            >
              <Text style={styles.testButtonText}>📱 Enviar Teste</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.sendButton, loading && styles.disabledButton]}
              onPress={handleSendMessage}
              disabled={loading || !title.trim() || !message.trim()}
            >
              {loading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <Text style={styles.sendButtonText}>🚀 Enviar Mensagem</Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>ℹ️ Informações</Text>
            <Text style={styles.infoText}>
              • A mensagem será enviada para todos os usuários com o app instalado
            </Text>
            <Text style={styles.infoText}>
              • Usuários receberão a notificação mesmo com o app fechado
            </Text>
            <Text style={styles.infoText}>
              • As mensagens ficam salvas na tela de notificações
            </Text>
            <Text style={styles.infoText}>
              • Use "Enviar Teste" para testar o sistema primeiro
            </Text>
          </View>
        </View>
      </ScrollView>

      <Footer
        currentScreen="Home"
        navigation={navigation}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Cores.textoEscuro,
    borderWidth: 1,
    borderColor: Cores.bordaClara,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  messageInput: {
    minHeight: 120,
    maxHeight: 200,
  },
  charCount: {
    fontSize: 12,
    color: Cores.textoMedio,
    textAlign: 'right',
    marginTop: 4,
    marginBottom: 8,
  },
  buttonContainer: {
    marginTop: 24,
    gap: 12,
  },
  button: {
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  testButton: {
    backgroundColor: '#6c757d',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sendButton: {
    backgroundColor: Cores.primaria,
  },
  sendButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: Cores.textoMedio,
    opacity: 0.6,
  },
  infoContainer: {
    marginTop: 32,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Cores.primaria,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: Cores.textoMedio,
    lineHeight: 20,
    marginBottom: 4,
  },
});

export default SendMessageScreen;
