import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import pushNotificationService from '../services/pushNotificationService';
import { nativeStorage } from '../utils/nativeStorage';
import Cores from '../constants/Cores';

interface TokenInfo {
  token: string | null;
  tokenType: string | null;
  isReal: boolean;
  environment: string;
  executionEnv: string;
  isDevice: boolean;
  timestamp: string;
}

interface DebugTokenScreenProps {
  navigation: {
    goBack: () => void;
    navigate: (screen: string) => void;
  };
}

export default function DebugTokenScreen({ navigation }: DebugTokenScreenProps) {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadTokenInfo = async () => {
    try {
      console.log('🔍 Carregando informações do token...');
      
      // Obter token atual
      const currentToken = pushNotificationService.getToken();
      const storedToken = await nativeStorage.getItem('push_token');
      const tokenType = await nativeStorage.getItem('token_type');
      
      const finalToken = currentToken || storedToken;
      const isReal = finalToken ? finalToken.startsWith('ExponentPushToken[') : false;
      
      const info: TokenInfo = {
        token: finalToken,
        tokenType: tokenType,
        isReal: isReal,
        environment: Constants.executionEnvironment || 'unknown',
        executionEnv: Constants.executionEnvironment || 'unknown',
        isDevice: Device.isDevice,
        timestamp: new Date().toLocaleString()
      };
      
      setTokenInfo(info);
      console.log('✅ Informações carregadas:', info);
      
    } catch (error) {
      console.error('❌ Erro ao carregar token info:', error);
      Alert.alert('Erro', 'Falha ao carregar informações do token');
    }
  };

  const reinitializeToken = async () => {
    try {
      Alert.alert(
        'Reinicializar Token',
        'Isso irá tentar obter um novo token. Continuar?',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Sim',
            onPress: async () => {
              console.log('🔄 Reinicializando token...');
              const newToken = await pushNotificationService.initialize();
              
              if (newToken) {
                Alert.alert(
                  'Token Reinicializado',
                  `Novo token: ${newToken.substring(0, 30)}...`
                );
                loadTokenInfo();
              } else {
                Alert.alert('Erro', 'Falha ao obter novo token');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('❌ Erro ao reinicializar:', error);
      Alert.alert('Erro', 'Falha ao reinicializar token');
    }
  };

  const copyToken = () => {
    if (tokenInfo?.token) {
      // Em um app real, você usaria Clipboard.setString
      Alert.alert(
        'Token Copiado',
        `Token: ${tokenInfo.token}`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadTokenInfo().finally(() => setRefreshing(false));
  };

  useEffect(() => {
    loadTokenInfo();
  }, []);

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>← Voltar</Text>
        </TouchableOpacity>
        <Text style={styles.title}>🔍 Debug Token</Text>
        <Text style={styles.subtitle}>Informações do Token Push</Text>
      </View>

      {tokenInfo ? (
        <View style={styles.content}>
          {/* Status do Token */}
          <View style={[styles.card, tokenInfo.isReal ? styles.successCard : styles.warningCard]}>
            <Text style={styles.cardTitle}>
              {tokenInfo.isReal ? '✅ TOKEN REAL' : '⚠️ TOKEN NÃO-REAL'}
            </Text>
            <Text style={styles.cardSubtitle}>
              {tokenInfo.isReal ? 'ExponentPushToken detectado' : 'Token simulado ou formato inválido'}
            </Text>
          </View>

          {/* Informações do Token */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>📱 Token Atual</Text>
            <Text style={styles.tokenText} numberOfLines={3}>
              {tokenInfo.token || 'Nenhum token encontrado'}
            </Text>
            <TouchableOpacity style={styles.copyButton} onPress={copyToken}>
              <Text style={styles.copyButtonText}>📋 Ver Token Completo</Text>
            </TouchableOpacity>
          </View>

          {/* Informações do Ambiente */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>🏭 Ambiente</Text>
            <Text style={styles.infoText}>Execution Environment: {tokenInfo.environment}</Text>
            <Text style={styles.infoText}>É Dispositivo: {tokenInfo.isDevice ? 'Sim' : 'Não'}</Text>
            <Text style={styles.infoText}>Tipo do Token: {tokenInfo.tokenType || 'Desconhecido'}</Text>
            <Text style={styles.infoText}>Última Atualização: {tokenInfo.timestamp}</Text>
          </View>

          {/* Ações */}
          <View style={styles.actions}>
            <TouchableOpacity style={styles.button} onPress={reinitializeToken}>
              <Text style={styles.buttonText}>🔄 Reinicializar Token</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={handleRefresh}>
              <Text style={styles.buttonText}>🔍 Atualizar Informações</Text>
            </TouchableOpacity>
          </View>

          {/* Instruções */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>📋 Instruções</Text>
            <Text style={styles.instructionText}>
              • Se mostrar "TOKEN REAL", o sistema está funcionando{'\n'}
              • Se mostrar "TOKEN NÃO-REAL", há problema na geração{'\n'}
              • Use "Reinicializar Token" para tentar novamente{'\n'}
              • Puxe para baixo para atualizar as informações
            </Text>
          </View>
        </View>
      ) : (
        <View style={styles.loading}>
          <Text style={styles.loadingText}>Carregando informações...</Text>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: Cores.primary,
    padding: 20,
    alignItems: 'center',
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 20,
    top: 20,
    zIndex: 1,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  content: {
    padding: 15,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  successCard: {
    borderLeftWidth: 5,
    borderLeftColor: '#4CAF50',
  },
  warningCard: {
    borderLeftWidth: 5,
    borderLeftColor: '#FF9800',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  tokenText: {
    fontSize: 12,
    fontFamily: 'monospace',
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#555',
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#555',
  },
  actions: {
    marginVertical: 10,
  },
  button: {
    backgroundColor: Cores.primary,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  copyButton: {
    backgroundColor: '#2196F3',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  copyButtonText: {
    color: 'white',
    fontSize: 14,
  },
  loading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 50,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
});
