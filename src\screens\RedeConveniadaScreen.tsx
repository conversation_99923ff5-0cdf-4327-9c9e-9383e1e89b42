import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
  Modal,
  FlatList,
} from 'react-native';
import Cores from '../constants/Cores';
import EstilosComuns from '../constants/Estilos';
import { useAppContext } from '../context/AppContext';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  listarEstados,
  listarCidades,
  listarSegmentos,
  Estado,
  Cidade,
  Segmento
} from '../services/api';

interface RedeConveniadaScreenProps {
  navigation: {
    navigate: (screen: 'Home' | 'RedeConveniadaResultados', params?: any) => void;
    goBack: () => void;
  };
}

const RedeConveniadaScreen = ({ navigation }: RedeConveniadaScreenProps) => {
  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack());

  const [estados, setEstados] = useState<Estado[]>([]);
  const [cidades, setCidades] = useState<Cidade[]>([]);
  const [segmentos, setSegmentos] = useState<Segmento[]>([]);
  
  const [estadoSelecionado, setEstadoSelecionado] = useState<string>('0');
  const [cidadeSelecionada, setCidadeSelecionada] = useState<string>('0');
  const [segmentoSelecionado, setSegmentoSelecionado] = useState<string>('0');
  
  const [loading, setLoading] = useState(false);
  const [loadingCidades, setLoadingCidades] = useState(false);
  const [loadingSegmentos, setLoadingSegmentos] = useState(false);

  // Estados para modais simples
  const [showEstadoModal, setShowEstadoModal] = useState(false);
  const [showCidadeModal, setShowCidadeModal] = useState(false);
  const [showSegmentoModal, setShowSegmentoModal] = useState(false);

  // Cache para evitar recarregamentos desnecessários
  const [estadosCarregados, setEstadosCarregados] = useState(false);
  const [cidadesCache, setCidadesCache] = useState<{[key: string]: Cidade[]}>({});
  const [segmentosCache, setSegmentosCache] = useState<{[key: string]: Segmento[]}>({});

  const { getUsuarioData } = useAppContext();

  // Carregar dados iniciais (com cache)
  useEffect(() => {
    if (!estadosCarregados) {
      carregarDadosIniciais();
    }
  }, [estadosCarregados]);

  // Carregar cidades quando estado mudar
  useEffect(() => {
    if (estadoSelecionado !== '0') {
      carregarCidades(estadoSelecionado);
    } else {
      carregarCidades();
    }
    // Reset cidade e segmento quando estado mudar
    setCidadeSelecionada('0');
    setSegmentoSelecionado('0');
  }, [estadoSelecionado]);

  // Carregar segmentos quando cidade mudar
  useEffect(() => {
    carregarSegmentos();
    // Reset segmento quando cidade mudar
    setSegmentoSelecionado('0');
  }, [cidadeSelecionada]);

  const carregarDadosIniciais = async () => {
    setLoading(true);
    try {
      const usuarioData = getUsuarioData();
      if (!usuarioData) {
        Alert.alert('Erro', 'Dados do usuário não encontrados');
        return;
      }

      const codent = usuarioData.usuario.codent.toString();

      // Carregar estados, cidades e segmentos iniciais
      const [estadosResponse, cidadesResponse, segmentosResponse] = await Promise.all([
        listarEstados(codent),
        listarCidades(codent),
        listarSegmentos(codent)
      ]);

      if (estadosResponse.success) {
        setEstados(estadosResponse.data || []);
        setEstadosCarregados(true);
        console.log('✅ Estados carregados e cacheados:', estadosResponse.data?.length || 0);
      } else {
        console.log('⚠️ Falha ao carregar estados:', estadosResponse.message);
      }

      if (cidadesResponse.success) {
        setCidades(cidadesResponse.data || []);
      } else {
        console.log('⚠️ Falha ao carregar cidades:', cidadesResponse.message);
      }

      if (segmentosResponse.success) {
        setSegmentos(segmentosResponse.data || []);
      } else {
        console.log('⚠️ Falha ao carregar segmentos:', segmentosResponse.message);
      }

      // Se nenhum dado foi carregado, mostrar erro
      if (!estadosResponse.success && !cidadesResponse.success && !segmentosResponse.success) {
        Alert.alert(
          'Erro de Conexão',
          'Não foi possível carregar os dados. Verifique sua conexão com a internet e tente novamente.',
          [
            { text: 'Tentar Novamente', onPress: () => carregarDadosIniciais() },
            { text: 'Voltar', onPress: () => navigation.goBack() }
          ]
        );
      }

    } catch (error) {
      console.error('❌ Erro ao carregar dados iniciais:', error);
      Alert.alert(
        'Erro de Conexão',
        'Não foi possível conectar ao servidor. Verifique sua conexão com a internet e tente novamente.',
        [
          { text: 'Tentar Novamente', onPress: () => carregarDadosIniciais() },
          { text: 'Voltar', onPress: () => navigation.goBack() }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const carregarCidades = async (uf?: string) => {
    setLoadingCidades(true);
    try {
      const usuarioData = getUsuarioData();
      if (!usuarioData) return;

      const codent = usuarioData.usuario.codent.toString();
      const response = await listarCidades(codent, uf);

      if (response.success) {
        setCidades(response.data || []);
      } else {
        console.log('⚠️ Falha ao carregar cidades:', response.message);
        // Não mostrar alert aqui, apenas log
      }
    } catch (error) {
      console.error('❌ Erro ao carregar cidades:', error);
      // Não mostrar alert aqui, apenas log
    } finally {
      setLoadingCidades(false);
    }
  };

  const carregarSegmentos = async () => {
    setLoadingSegmentos(true);
    try {
      const usuarioData = getUsuarioData();
      if (!usuarioData) return;

      const codent = usuarioData.usuario.codent.toString();
      const uf = estadoSelecionado !== '0' ? estadoSelecionado : undefined;
      const cidade = cidadeSelecionada !== '0' ? cidadeSelecionada : undefined;
      
      const response = await listarSegmentos(codent, uf, cidade);

      if (response.success) {
        setSegmentos(response.data || []);
      }
    } catch (error) {
      console.error('❌ Erro ao carregar segmentos:', error);
    } finally {
      setLoadingSegmentos(false);
    }
  };

  // Funções para obter labels
  const getEstadoLabel = () => {
    if (estadoSelecionado === '0') return 'Todos os Estados';
    const estado = estados.find(e => e.uf === estadoSelecionado);
    return estado ? estado.uf : 'Selecionar Estado';
  };

  const getCidadeLabel = () => {
    if (cidadeSelecionada === '0') return 'Todas as Cidades';
    const cidade = cidades.find(c => c.codigo === cidadeSelecionada);
    return cidade ? cidade.cidade : 'Selecionar Cidade';
  };

  const getSegmentoLabel = () => {
    if (segmentoSelecionado === '0') return 'Todos os Segmentos';
    const segmento = segmentos.find(s => s.codseg === segmentoSelecionado);
    return segmento ? segmento.desseg : 'Selecionar Segmento';
  };

  const handleBuscar = () => {
    console.log('🔍 Buscando com filtros:', {
      estado: estadoSelecionado,
      cidade: cidadeSelecionada,
      segmento: segmentoSelecionado
    });

    navigation.navigate('RedeConveniadaResultados', {
      estado: estadoSelecionado,
      cidade: cidadeSelecionada,
      segmento: segmentoSelecionado
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Rede Conveniada</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingSpinner message="Carregando dados..." size="large" />
          </View>
        ) : (
          <>
            {/* Estado */}
            <View style={styles.filterContainer}>
              <Text style={styles.filterLabel}>Estado</Text>
              <TouchableOpacity
                style={styles.selectorButton}
                onPress={() => setShowEstadoModal(true)}
              >
                <Text style={styles.selectorText}>{getEstadoLabel()}</Text>
                <Text style={styles.selectorArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Cidade */}
            <View style={styles.filterContainer}>
              <Text style={styles.filterLabel}>Cidade</Text>
              <TouchableOpacity
                style={[styles.selectorButton, loadingCidades && styles.selectorDisabled]}
                onPress={() => !loadingCidades && setShowCidadeModal(true)}
                disabled={loadingCidades}
              >
                {loadingCidades ? (
                  <View style={styles.loadingSelectorContainer}>
                    <ActivityIndicator size="small" color={Cores.primaria} />
                    <Text style={styles.loadingSelectorText}>Carregando...</Text>
                  </View>
                ) : (
                  <>
                    <Text style={styles.selectorText}>{getCidadeLabel()}</Text>
                    <Text style={styles.selectorArrow}>▼</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Segmento */}
            <View style={styles.filterContainer}>
              <Text style={styles.filterLabel}>Segmento</Text>
              <TouchableOpacity
                style={[styles.selectorButton, loadingSegmentos && styles.selectorDisabled]}
                onPress={() => !loadingSegmentos && setShowSegmentoModal(true)}
                disabled={loadingSegmentos}
              >
                {loadingSegmentos ? (
                  <View style={styles.loadingSelectorContainer}>
                    <ActivityIndicator size="small" color={Cores.primaria} />
                    <Text style={styles.loadingSelectorText}>Carregando...</Text>
                  </View>
                ) : (
                  <>
                    <Text style={styles.selectorText}>{getSegmentoLabel()}</Text>
                    <Text style={styles.selectorArrow}>▼</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Botão Buscar */}
            <TouchableOpacity
              style={styles.buscarButton}
              onPress={handleBuscar}
            >
              <Text style={styles.buscarButtonText}>Buscar</Text>
            </TouchableOpacity>
          </>
        )}
      </ScrollView>

      {/* Modal Estado */}
      <Modal
        visible={showEstadoModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowEstadoModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Selecionar Estado</Text>
              <TouchableOpacity onPress={() => setShowEstadoModal(false)}>
                <Text style={styles.modalClose}>✕</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={[{ uf: '0', nome: 'Todos os Estados' }, ...estados.map(e => ({ uf: e.uf, nome: e.uf }))]}
              keyExtractor={(item) => item.uf}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.modalItem, estadoSelecionado === item.uf && styles.modalItemSelected]}
                  onPress={() => {
                    setEstadoSelecionado(item.uf);
                    setShowEstadoModal(false);
                  }}
                >
                  <Text style={[styles.modalItemText, estadoSelecionado === item.uf && styles.modalItemTextSelected]}>
                    {item.nome}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>

      {/* Modal Cidade */}
      <Modal
        visible={showCidadeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCidadeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Selecionar Cidade</Text>
              <TouchableOpacity onPress={() => setShowCidadeModal(false)}>
                <Text style={styles.modalClose}>✕</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={[{ codigo: '0', cidade: 'Todas as Cidades' }, ...cidades]}
              keyExtractor={(item) => item.codigo}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.modalItem, cidadeSelecionada === item.codigo && styles.modalItemSelected]}
                  onPress={() => {
                    setCidadeSelecionada(item.codigo);
                    setShowCidadeModal(false);
                  }}
                >
                  <Text style={[styles.modalItemText, cidadeSelecionada === item.codigo && styles.modalItemTextSelected]}>
                    {item.cidade}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>

      {/* Modal Segmento */}
      <Modal
        visible={showSegmentoModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSegmentoModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Selecionar Segmento</Text>
              <TouchableOpacity onPress={() => setShowSegmentoModal(false)}>
                <Text style={styles.modalClose}>✕</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={[{ codseg: '0', desseg: 'Todos os Segmentos' }, ...segmentos]}
              keyExtractor={(item) => item.codseg}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.modalItem, segmentoSelecionado === item.codseg && styles.modalItemSelected]}
                  onPress={() => {
                    setSegmentoSelecionado(item.codseg);
                    setShowSegmentoModal(false);
                  }}
                >
                  <Text style={[styles.modalItemText, segmentoSelecionado === item.codseg && styles.modalItemTextSelected]}>
                    {item.desseg}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: 10,
    paddingBottom: 10,
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Cores.textoMedio,
  },
  filterContainer: {
    marginTop: 20,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.primaria,
    marginBottom: 8,
  },
  selectorButton: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Cores.bordaClara,
    minHeight: 55,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectorDisabled: {
    opacity: 0.6,
    backgroundColor: '#f5f5f5',
  },
  selectorText: {
    color: Cores.textoEscuro,
    fontSize: 16,
    flex: 1,
    fontWeight: '500',
  },
  selectorArrow: {
    color: Cores.primaria,
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  loadingSelectorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  loadingSelectorText: {
    marginLeft: 10,
    color: Cores.textoMedio,
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    width: '85%',
    maxHeight: '70%',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: Cores.primaria,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  modalClose: {
    fontSize: 22,
    color: 'white',
    fontWeight: 'bold',
  },
  modalItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalItemSelected: {
    backgroundColor: Cores.primaria + '15',
  },
  modalItemText: {
    fontSize: 16,
    color: Cores.textoEscuro,
  },
  modalItemTextSelected: {
    color: Cores.primaria,
    fontWeight: 'bold',
  },
  buscarButton: {
    backgroundColor: Cores.primaria,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buscarButtonText: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default RedeConveniadaScreen;
