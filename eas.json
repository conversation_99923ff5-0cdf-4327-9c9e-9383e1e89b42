{"cli": {"version": ">= 16.17.0", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "credentialsSource": "local"}}, "test": {"distribution": "internal", "android": {"buildType": "apk", "credentialsSource": "local"}}, "production": {"android": {"buildType": "app-bundle", "credentialsSource": "remote"}, "autoIncrement": false}, "production-aab": {"extends": "production", "android": {"buildType": "app-bundle", "credentialsSource": "local"}}}, "submit": {"production": {}}}