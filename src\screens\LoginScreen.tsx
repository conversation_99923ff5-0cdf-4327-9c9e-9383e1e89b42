import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, StatusBar, KeyboardAvoidingView, Platform, Linking } from 'react-native';
// Usando Storage Nativo JavaScript + Firebase React Native OFICIAL
import { login, getAutorizacoesPendentes } from '../services/api';
import { useAppContext } from '../context/AppContext';
import Cores from '../constants/Cores';
import EstilosComuns from '../constants/Estilos';
import LoadingSpinner from '../components/LoadingSpinner';
import TokenDebugInfo from '../components/TokenDebugInfo';
import { saveLoginData as saveRobustLoginData, loadLoginData as loadRobustLoginData, robustStorage } from '../utils/robustStorage';
import pushNotificationService from '../services/pushNotificationService';
import biometricService from '../services/biometricService';
import { usePushNotifications } from '../store/pushNotificationStore';
import ResponsiveScrollView from '../components/ResponsiveScrollView';
import ResponsiveText from '../components/ResponsiveText';
import {
  getResponsivePadding,
  getResponsiveButtonHeight,
  getResponsiveSpacing,
  getResponsiveTextSize,
  getResponsiveTitleSize,
  isSmallScreen
} from '../utils/responsive';
import biometricConfig, {
  getBiometricUserChoice,
  setBiometricUserChoice,
  shouldAutoLoginWithBiometric,
  enableBiometricConfig,
  hasUserMadeFirstBiometricChoice,
  shouldRequirePasswordFirst,
  markPasswordLoginDone,
  resetPasswordRequirement
} from '../utils/biometricConfig';
import { isAndroid8OrLower } from '../utils/networkConfig';
import { logDeviceInfo } from '../utils/android8TestUtils';

// Storage simples usando variáveis globais (temporário até resolver AsyncStorage)
let globalStorage: { [key: string]: string } = {};

// Tornar globalStorage acessível globalmente
(globalThis as any).globalStorage = globalStorage;

interface LoginScreenProps {
  navigation: {
    navigate: (screen: 'Login' | 'Home' | 'EsqueceuSenha' | 'AutorizacoesPendentes' | 'AlterarSenha', params?: any) => void;
  };
}

const LoginScreen = ({ navigation }: LoginScreenProps) => {
  const { setUserData } = useAppContext();
  const [cardOrEmail, setCardOrEmail] = useState('');
  const [password, setPassword] = useState('');
  const [mostrarSenha, setmostrarSenha] = useState(false);
  const [rememberData, setRememberData] = useState(false);
  const [isEmailMode, setIsEmailMode] = useState(false); // false = cartão, true = email
  const [loading, setLoading] = useState(false);
  // Estados para biometria - DESABILITADO TEMPORARIAMENTE
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricUserChoice, setBiometricUserChoice] = useState(false); // Escolha do usuário
  const [biometricCheckboxText, setBiometricCheckboxText] = useState('🔐 Habilitar digital');
  const [showBiometricButton, setShowBiometricButton] = useState(false); // Controla quando mostrar botão
  const BIOMETRIC_DISABLED = true; // Flag para desabilitar biometria completamente
  const [showTokenDebug, setShowTokenDebug] = useState(false);

  // Push notifications para debug
  const { fcmToken } = usePushNotifications();

  // Função para abrir WhatsApp
  const abrirWhatsApp = async () => {
    const numeroWhatsApp = '555132874300';
    const mensagem = 'Olá, preciso de suporte.';
    const url = `whatsapp://send?phone=${numeroWhatsApp}&text=${encodeURIComponent(mensagem)}`;

    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        // Fallback para web WhatsApp
        const webUrl = `https://wa.me/${numeroWhatsApp}?text=${encodeURIComponent(mensagem)}`;
        await Linking.openURL(webUrl);
      }
    } catch (error) {
      console.error('❌ Erro ao abrir WhatsApp:', error);
      Alert.alert('Erro', 'Não foi possível abrir o WhatsApp. Tente ligar para (51) 3287-4300');
    }
  };

  // Debug: Mostrar token FCM quando disponível no LoginScreen
  useEffect(() => {
    if (fcmToken) {
      console.log('🎯 TOKEN FCM REAL no LoginScreen:', fcmToken);

      // Verificar se é token real ou simulado
      const isRealToken = fcmToken.startsWith('ExponentPushToken') || fcmToken.length > 100;
      const tokenType = fcmToken.startsWith('ExponentPushToken') ? 'Expo Push Token' :
        fcmToken.startsWith('tecbiz_dev_') ? 'Token Simulado (Fallback)' :
          'Firebase FCM Token';

      console.log('📱 Tipo de token:', tokenType);
      console.log('✅ É token real?', isRealToken);
      console.log('🎯 Token será enviado no login automaticamente');

      // Remover alert automático - apenas logs
      // O token será enviado automaticamente no login
    }
  }, [fcmToken]);

  // Carregar dados salvos ao inicializar
  useEffect(() => {
    const initializeScreen = async () => {
      // Log simples para Android 8.0 (sem testes automáticos)
      if (isAndroid8OrLower()) {
        console.log('📱 Android 8.0 detectado - usando configurações compatíveis');
        logDeviceInfo();
      }

      await loadSavedData();
      await initializeBiometric();
    };

    initializeScreen();

    // Cleanup: Resetar exigência de senha quando componente é desmontado
    return () => {
      resetPasswordRequirement().catch(console.error);
    };
  }, []);

  // Inicializar biometria - DESABILITADO
  const initializeBiometric = async () => {
    if (BIOMETRIC_DISABLED) {
      console.log('🔐 Biometria desabilitada por configuração');
      setBiometricAvailable(false);
      setBiometricEnabled(false);
      setBiometricUserChoice(false);
      setShowBiometricButton(false);
      return;
    }

    try {
      // Inicializar configuração biométrica
      await biometricConfig.initialize();

      const available = await biometricService.initialize();
      setBiometricAvailable(available);

      if (available) {
        const enabled = await biometricService.isBiometricLoginEnabled();
        setBiometricEnabled(enabled);

        // Carregar escolha do usuário da nova configuração
        const userChoice = await getBiometricUserChoice();
        setBiometricUserChoice(userChoice);

        // Atualizar texto do checkbox
        const checkboxText = await getBiometricCheckboxText();
        setBiometricCheckboxText(checkboxText);

        console.log('🔐 Biometria disponível:', available, 'Habilitada:', enabled, 'Escolha usuário:', userChoice);

        // NOVA LÓGICA: Verificar se deve mostrar botão de biometria
        const requirePassword = await shouldRequirePasswordFirst();
        console.log('🔐 Requer senha primeiro:', requirePassword);

        if (requirePassword) {
          console.log('🔐 Primeira vez ou senha obrigatória - usuário deve digitar senha');
          setShowBiometricButton(false); // Não mostrar botão até fazer login com senha
        } else {
          console.log('🔐 Biometria liberada - usuário pode escolher entre senha ou biometria');
          setShowBiometricButton(enabled); // Mostrar botão se biometria estiver habilitada
        }
      }
    } catch (error) {
      console.log('❌ Erro ao inicializar biometria:', error);
    }
  };

  // Função para carregar dados salvos - CORRIGIDA PARA ASYNC
  const loadSavedData = async () => {
    try {
      console.log('📂 Carregando dados salvos...');
      const data = await loadRobustLoginData();

      console.log('📂 Dados "Lembrar dados":', {
        cardOrEmail: data.cardOrEmail ? data.cardOrEmail.substring(0, 10) + '...' : 'vazio',
        password: data.password ? '***' : 'vazio',
        rememberData: data.rememberData
      });

      if (data.rememberData && data.cardOrEmail) {
        setCardOrEmail(data.cardOrEmail);
        setPassword(data.password || '');
        setRememberData(true);

        // Detectar se é email
        setIsEmailMode(isEmail(data.cardOrEmail));
        console.log('✅ Dados "Lembrar dados" carregados na interface');
      }

      // Verificar dados para recarregamento automático
      const cartaoLogin = await robustStorage.getItem('cartao_login');
      const senhaLogin = await robustStorage.getItem('senha_login');
      console.log('🔄 Dados para recarregamento automático:');
      console.log('   - Cartão:', cartaoLogin ? cartaoLogin.substring(0, 10) + '...' : 'não encontrado');
      console.log('   - Senha:', senhaLogin ? '***' : 'não encontrada');

      if (cartaoLogin && senhaLogin) {
        console.log('✅ Dados para recarregamento automático disponíveis');
      } else {
        console.log('⚠️ Dados para recarregamento automático não encontrados');
      }

    } catch (error) {
      console.error('❌ Erro ao carregar dados salvos:', error);
    }
  };

  // Função para salvar dados - CORRIGIDA PARA USAR ROBUST STORAGE
  const saveData = async (cardData: string, passwordData: string, remember: boolean) => {
    try {
      console.log('💾 Salvando dados de login...');
      console.log('📧 Cartão/Email:', cardData.substring(0, 10) + '...');
      console.log('🔐 Senha:', passwordData ? '***' : 'vazia');
      console.log('💭 Lembrar:', remember);

      // Salvar dados para "Lembrar dados" usando robustStorage
      await saveRobustLoginData(cardData, passwordData, remember);

      // SEMPRE salvar para recarregamento automático
      await robustStorage.setItem('cartao_login', cardData);
      await robustStorage.setItem('senha_login', passwordData);

      console.log('✅ Dados salvos com sucesso no storage robusto!');
    } catch (error) {
      console.error('❌ Erro ao salvar dados:', error);
    }
  };

  // FUNÇÃO DE TESTE STORAGE ROBUSTO
  const testStorage = async () => {
    try {
      const info = `Storage Robusto Ativo\nMemória + AsyncStorage`;
      Alert.alert(
        'Teste Storage Robusto',
        `${info}\n\nDeseja testar operações básicas?`,
        [
          {
            text: 'Testar',
            onPress: () => testStorageOperations()
          },
          {
            text: 'Ver Dados',
            onPress: () => showStorageData()
          },
          {
            text: 'Cancelar',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('❌ Erro no teste de storage:', error);
    }
  };

  const testStorageOperations = async () => {
    try {
      // Teste básico de escrita e leitura
      await robustStorage.setItem('test_key', 'test_value');
      const value = await robustStorage.getItem('test_key');
      await robustStorage.removeItem('test_key');

      Alert.alert(
        'Teste Storage Robusto',
        `✅ Sucesso!\n\nEscreveu: "test_value"\nLeu: "${value}"\n\nStorage funcionando corretamente!`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Teste Storage Nativo',
        `❌ Erro no teste:\n\n${error}`,
        [{ text: 'OK' }]
      );
    }
  };

  const showStorageData = async () => {
    try {
      const cardOrEmail = await robustStorage.getItem('saved_cardOrEmail');
      const rememberData = await robustStorage.getBoolean('saved_rememberData');
      const cartaoLogin = await robustStorage.getItem('cartao_login');

      Alert.alert(
        'Dados do Storage',
        `📂 Dados "Lembrar":\n` +
        `   - Cartão/Email: ${cardOrEmail || 'vazio'}\n` +
        `   - Lembrar: ${rememberData}\n\n` +
        `🔄 Dados Automáticos:\n` +
        `   - Cartão Login: ${cartaoLogin || 'vazio'}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Erro', `❌ Erro ao ler dados:\n${error}`, [{ text: 'OK' }]);
    }
  };

  // FUNÇÃO DE TESTE FIREBASE REAL
  const testFirebase = () => {
    Alert.alert(
      'Teste Push Notifications',
      `${JSON.stringify(pushNotificationService.getServiceInfo(), null, 2)}\n\nDeseja testar operações?`,
      [
        {
          text: 'Ver Notificações',
          onPress: () => {
            const notifications = pushNotificationService.getNotifications();
            const count = notifications.length;
            const unread = pushNotificationService.getUnreadCount();
            const token = pushNotificationService.getToken();
            Alert.alert(
              'Notificações Push',
              `Total: ${count}\nNão lidas: ${unread}\nToken: ${token ? 'OBTIDO' : 'Não obtido'}\n\nÚltimas notificações:\n${notifications.slice(0, 3).map((n: any) => `• ${n.title}`).join('\n')}\n\n💡 Para testar notificações, use sua tela PHP de admin.`,
              [{ text: 'OK' }]
            );
          }
        },
        {
          text: 'Cancelar',
          style: 'cancel'
        }
      ]
    );
  };

  // Função para formatar cartão com pontos
  const formatCard = (text: string): string => {
    // Remove tudo que não é número
    const numbersOnly = text.replace(/\D/g, '');

    // Limita a 16 dígitos
    const limited = numbersOnly.substring(0, 16);

    // Adiciona pontos a cada 4 dígitos
    const formatted = limited.replace(/(\d{4})(?=\d)/g, '$1.');

    return formatted;
  };

  // Função para detectar se é email
  const isEmail = (text: string): boolean => {
    return text.includes('@');
  };

  // Handler para mudança no campo cartão/email
  const handleCardOrEmailChange = (text: string) => {
    if (isEmail(text) || isEmail(cardOrEmail)) {
      // Se é email ou estava sendo digitado email, não formata
      setCardOrEmail(text);
    } else {
      // Se é cartão, aplica formatação
      const formatted = formatCard(text);
      setCardOrEmail(formatted);
    }
  };

  // Login com biometria
  const handleBiometricLogin = async () => {
    try {
      setLoading(true);
      console.log('🔐 Tentando login biométrico...');

      const result = await biometricService.authenticateLogin();

      if (result.success && result.cardOrEmail && result.password) {
        console.log('✅ Autenticação biométrica bem-sucedida');

        // Fazer login com os dados obtidos
        const response = await login(result.cardOrEmail, result.password, fcmToken || undefined);

        if (response.success) {
          console.log('✅ Login biométrico realizado com sucesso');

          // Salvar dados no contexto
          setUserData(response.data);

          // ✨ NOVO: Salvar dados do usuário no storage para notificações (biométrico)
          try {
            const userData = Array.isArray(response.data)
              ? response.data.find(item => 'usuario' in item)
              : null;

            if (userData && userData.usuario && userData.usuario.codass && userData.usuario.codent) {
              const userInfo = {
                codass: userData.usuario.codass,
                codent: userData.usuario.codent,
                portador: userData.usuario.portador,
                carass: userData.usuario.carass
              };

              await robustStorage.setItem('user_data', JSON.stringify(userInfo));
              console.log('💾 Dados do usuário salvos para notificações (biométrico):', {
                codass: userInfo.codass,
                codent: userInfo.codent
              });
            }
          } catch (error) {
            console.error('❌ Erro ao salvar dados do usuário (biométrico):', error);
          }

          // Processar resposta igual ao login normal
          await processLoginSuccess(response.data);
        } else {
          Alert.alert('Erro', response.message);
        }
      } else {
        console.log('❌ Falha na autenticação biométrica');
      }
    } catch (error) {
      console.error('❌ Erro no login biométrico:', error);
      Alert.alert('Erro', 'Falha no login biométrico');
    } finally {
      setLoading(false);
    }
  };

  // Obter texto dinâmico para checkbox de biometria
  const getBiometricCheckboxText = async (): Promise<string> => {
    try {
      const hasFirstChoice = await hasUserMadeFirstBiometricChoice();

      if (!hasFirstChoice) {
        return '🔐 Habilitar digital';
      } else if (biometricUserChoice && biometricEnabled) {
        return '🔐 Digital habilitada';
      } else if (biometricUserChoice && !biometricEnabled) {
        return '🔐 Configurar digital';
      } else {
        return '🔐 Habilitar digital';
      }
    } catch (error) {
      return '🔐 Habilitar digital';
    }
  };

  // Salvar escolha do usuário sobre biometria
  const saveBiometricUserChoice = async (choice: boolean) => {
    try {
      // Usar nova configuração biométrica
      await setBiometricUserChoice(choice);
      setBiometricUserChoice(choice);
      console.log('💾 Escolha de biometria salva:', choice);

      // Atualizar texto do checkbox
      const checkboxText = await getBiometricCheckboxText();
      setBiometricCheckboxText(checkboxText);

      // Se desabilitou, limpar dados biométricos
      if (!choice && biometricEnabled) {
        await biometricService.disableBiometricLogin();
        setBiometricEnabled(false);
        console.log('🗑️ Dados biométricos removidos');
      }
    } catch (error) {
      console.error('❌ Erro ao salvar escolha de biometria:', error);
    }
  };

  // Configurar biometria após login bem-sucedido (apenas se usuário escolheu)
  const setupBiometricAfterLogin = async () => {
    try {
      if (biometricAvailable && biometricUserChoice && !biometricEnabled && cardOrEmail && password) {
        console.log('🔐 Configurando biometria silenciosamente (usuário escolheu habilitar)...');
        const saved = await biometricService.saveBiometricData(cardOrEmail, password);
        if (saved) {
          setBiometricEnabled(true);
          // Habilitar na configuração global com auto-login
          await enableBiometricConfig(true);
          console.log('✅ Biometria configurada com sucesso (silencioso)');
        }
      } else {
        console.log('🔐 Biometria não será configurada');
        console.log('   - Disponível:', biometricAvailable);
        console.log('   - Usuário escolheu:', biometricUserChoice);
        console.log('   - Já habilitada:', biometricEnabled);
        console.log('   - Dados:', !!cardOrEmail && !!password);
      }
    } catch (error) {
      console.log('❌ Erro ao configurar biometria:', error);
    }
  };

  // Processar sucesso do login (extraído para reutilizar)
  const processLoginSuccess = async (responseData: any) => {
    try {
      // ✨ NOVO: Limpar dados do usuário anterior antes de processar novo login
      console.log('🧹 Limpando dados do usuário anterior...');
      try {
        await pushNotificationService.clearPreviousUserData();
        console.log('✅ Dados do usuário anterior limpos');
      } catch (error) {
        console.error('❌ Erro ao limpar dados do usuário anterior:', error);
      }

      // ✨ NOVO: Recarregar notificações para o novo usuário
      console.log('🔄 Recarregando notificações para o usuário logado...');
      try {
        await pushNotificationService.reloadUserNotifications();
        console.log('✅ Notificações recarregadas com sucesso');
      } catch (error) {
        console.error('❌ Erro ao recarregar notificações:', error);
      }

      // Buscar dados do usuário para pegar codass e codent
      console.log('📊 Dados da resposta:', JSON.stringify(responseData, null, 2));
      console.log('📊 Tipo da resposta:', typeof responseData);
      console.log('📊 É array?', Array.isArray(responseData));

      let userData = null;
      if (Array.isArray(responseData)) {
        console.log('🔍 Procurando userData no array...');
        responseData.forEach((item, index) => {
          console.log(`🔍 Item ${index}:`, JSON.stringify(item, null, 2));
          console.log(`🔍 Item ${index} é objeto?`, item && typeof item === 'object');
          console.log(`🔍 Item ${index} tem 'usuario'?`, item && 'usuario' in item);
        });

        userData = responseData.find(item => {
          const hasUsuario = item && typeof item === 'object' && 'usuario' in item;
          console.log('🔍 Item tem usuario?', hasUsuario, item);
          return hasUsuario;
        });
      }

      console.log('👤 UserData encontrado:', JSON.stringify(userData, null, 2));

      if (userData && userData.usuario) {
        console.log('✅ userData.usuario existe');
        console.log('🔑 userData.usuario.codass:', userData.usuario.codass);
        console.log('🔑 userData.usuario.codent:', userData.usuario.codent);
        console.log('🔑 userData.usuario.senhaPadrao:', userData.usuario.senhaPadrao);

        // Verificar se precisa alterar senha (senhaPadrao = true)
        const senhaPadrao = String(userData.usuario.senhaPadrao).toLowerCase();
        if (senhaPadrao === 'true') {
          console.log('🔐 SENHA PADRÃO DETECTADA - Redirecionando para AlterarSenha');
          navigation.navigate('AlterarSenha', {
            fromLogin: true,
            userData: userData.usuario,
            hideMenu: true,
            senhaInicial: true
          });
          return;
        }

        if (userData.usuario.codass && userData.usuario.codent) {
          const { codass, codent } = userData.usuario;
          console.log('🔑 Extraindo - Codass:', codass, 'Codent:', codent);

          // Verificar autorizações pendentes
          console.log('🔍 Verificando autorizações pendentes...');
          try {
            const autorizacoesResponse = await getAutorizacoesPendentes(codass, codent);
            console.log('📦 Resposta autorizações:', autorizacoesResponse);

            if (autorizacoesResponse.success && autorizacoesResponse.data && autorizacoesResponse.data.extrato && autorizacoesResponse.data.extrato.length > 0) {
              console.log('✅ TEM AUTORIZAÇÕES - Encontradas:', autorizacoesResponse.data.extrato.length);
              console.log('🧭 Indo para AutorizacoesPendentes (com autorizações)');

              navigation.navigate('AutorizacoesPendentes', {
                codass,
                codent,
                autorizacoes: autorizacoesResponse.data,
                userData: responseData
              });
            } else {
              console.log('ℹ️ SEM AUTORIZAÇÕES - Array vazio');
              console.log('🧭 Indo para Home (sem autorizações)');
              navigation.navigate('Home', responseData);
            }

          } catch (error) {
            console.error('❌ Erro ao verificar autorizações:', error);
            console.log('🧭 Indo para Home (erro na verificação)');
            navigation.navigate('Home', responseData);
          }
        } else {
          console.log('❌ Codass ou Codent não encontrados');
          console.log('⚠️ Codass/Codent não encontrados, indo para Home');
          navigation.navigate('Home', responseData);
        }
      } else {
        console.log('❌ userData ou userData.usuario não existe');
        console.log('⚠️ UserData inválido, indo para Home');
        navigation.navigate('Home', { userData: responseData });
      }
    } catch (error) {
      console.error('❌ Erro ao processar login:', error);
      navigation.navigate('Home', responseData);
    }
  };

  const handleLogin = async () => {
    if (!cardOrEmail.trim()) {
      Alert.alert('Erro', 'Por favor, informe o cartão ou e-mail');
      return;
    }
    if (!password.trim()) {
      Alert.alert('Erro', 'Por favor, informe a senha');
      return;
    }

    setLoading(true);
    try {
      // Salva os dados (sempre para recarregamento, condicionalmente para lembrar)
      await saveData(cardOrEmail, password, rememberData);

      const response = await login(cardOrEmail, password, fcmToken || undefined);
      if (response.success) {
        // Salvar dados usando função corrigida
        await saveData(cardOrEmail, password, rememberData);
        console.log('💾 Dados salvos com storage nativo JavaScript');

        // Salva os dados no contexto global
        setUserData(response.data);

        // ✨ NOVO: Salvar dados do usuário no storage para notificações
        try {
          const userData = Array.isArray(response.data)
            ? response.data.find(item => 'usuario' in item)
            : null;

          if (userData && userData.usuario && userData.usuario.codass && userData.usuario.codent) {
            const userInfo = {
              codass: userData.usuario.codass,
              codent: userData.usuario.codent,
              portador: userData.usuario.portador,
              carass: userData.usuario.carass
            };

            await robustStorage.setItem('user_data', JSON.stringify(userInfo));
            console.log('💾 Dados do usuário salvos para notificações:', {
              codass: userInfo.codass,
              codent: userInfo.codent
            });
          }
        } catch (error) {
          console.error('❌ Erro ao salvar dados do usuário:', error);
        }

        // NOVA LÓGICA: Marcar que usuário fez login com senha
        await markPasswordLoginDone();
        console.log('✅ Login com senha realizado - biometria liberada para próximas vezes');

        // Habilitar botão de biometria para próximas vezes
        if (biometricAvailable && biometricEnabled) {
          setShowBiometricButton(true);
        }

        // Configurar biometria se disponível e não configurada
        await setupBiometricAfterLogin();

        // Processar sucesso do login
        await processLoginSuccess(response.data);


      } else {
        Alert.alert('Erro', response.message);
      }
    } catch (error) {
      console.log('Erro no login:', error);
      Alert.alert('Erro', 'Falha ao conectar ao servidor.');
    } finally {
      setLoading(false);
    }
  };

  // Função para acessar menu de desenvolvedor (toque longo no título)
  const handleDeveloperMenu = () => {
    Alert.alert(
      'Menu Desenvolvedor',
      'Escolha uma opção de teste:',
      [
        {
          text: 'Testar Storage',
          onPress: testStorage
        },
        {
          text: 'Testar Firebase',
          onPress: testFirebase
        },
        {
          text: 'Cancelar',
          style: 'cancel'
        }
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar backgroundColor={Cores.fundoPrincipal} barStyle="dark-content" />

      <ResponsiveScrollView
        containerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        enableAutoHorizontalScroll={true}
      >
        {/* Header com gradiente sutil */}
        <View style={styles.header}>
          <TouchableOpacity
            onLongPress={handleDeveloperMenu}
            activeOpacity={1}
          >
            <ResponsiveText
              variant="title"
              size="large"
              style={styles.appTitle}
              adjustsFontSizeToFit={true}
              minimumFontScale={0.8}
            >
              TecBiz Associados
            </ResponsiveText>
            <ResponsiveText
              variant="caption"
              style={styles.appSubtitle}
              adjustsFontSizeToFit={true}
              minimumFontScale={0.8}
            >
              Gestão de Convênios
            </ResponsiveText>
          </TouchableOpacity>
        </View>

        {/* Card de Login */}
        <View style={styles.loginCard}>
          <View style={styles.welcomeSection}>
            <ResponsiveText
              variant="title"
              size="medium"
              style={styles.welcomeTitle}
              adjustsFontSizeToFit={true}
              minimumFontScale={0.8}
            >
              Bem-vindo
            </ResponsiveText>
            <ResponsiveText
              variant="body"
              style={styles.welcomeSubtitle}
              adjustsFontSizeToFit={true}
              minimumFontScale={0.8}
            >
              Faça seu login para continuar
            </ResponsiveText>
          </View>

          {/* Campo Cartão/Email */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>{isEmailMode ? 'E-mail' : 'Cartão'}</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder={isEmailMode ? 'Digite seu e-mail' : 'Digite seu cartão'}
                value={cardOrEmail}
                onChangeText={(text) => {
                  if (isEmailMode) {
                    setCardOrEmail(text);
                  } else {
                    const cleaned = text.replace(/\D/g, '');
                    const limited = cleaned.substring(0, 16);
                    const formatted = limited.replace(/(\d{4})(?=\d)/g, '$1.');
                    setCardOrEmail(formatted);
                  }
                }}
                placeholderTextColor={Cores.textoClaro}
                autoCapitalize="none"
                keyboardType={isEmailMode ? 'email-address' : 'numeric'}
              />
              <TouchableOpacity
                style={styles.inputToggle}
                onPress={() => {
                  setIsEmailMode(!isEmailMode);
                  setCardOrEmail('');
                }}
              >
                <Text style={styles.inputToggleIcon}>
                  {isEmailMode ? '💳' : '📧'}
                </Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.inputHelper}>
              {isEmailMode ? 'Toque em 💳 para usar cartão' : 'Toque em 📧 para usar e-mail'}
            </Text>
          </View>

          {/* Campo Senha */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Senha</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Digite sua senha"
                value={password}
                onChangeText={(text) => {
                  const numericText = text.replace(/\D/g, '').substring(0, 6);
                  setPassword(numericText);
                }}
                secureTextEntry={!mostrarSenha}
                placeholderTextColor={Cores.textoClaro}
                keyboardType="numeric"
                maxLength={6}
              />
              <TouchableOpacity
                style={styles.inputToggle}
                onPress={() => setmostrarSenha(!mostrarSenha)}
              >
                <Text style={styles.inputToggleIcon}>
                  {mostrarSenha ? '🚫' : '👁️'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Checkbox Lembrar dados */}
          <TouchableOpacity
            style={styles.rememberContainer}
            onPress={() => setRememberData(!rememberData)}
          >
            <View style={[styles.rememberCheckbox, rememberData && styles.rememberCheckboxActive]}>
              {rememberData && <Text style={styles.rememberCheckmark}>✓</Text>}
            </View>
            <Text style={styles.rememberLabel}>Lembrar meus dados</Text>
          </TouchableOpacity>

          {/* Checkbox Habilitar Biometria */}
          {biometricAvailable && (
            <TouchableOpacity
              style={styles.rememberContainer}
              onPress={() => saveBiometricUserChoice(!biometricUserChoice)}
            >
              <View style={[styles.rememberCheckbox, biometricUserChoice && styles.rememberCheckboxActive]}>
                {biometricUserChoice && <Text style={styles.rememberCheckmark}>✓</Text>}
              </View>
              <Text style={styles.rememberLabel}>{biometricCheckboxText}</Text>
            </TouchableOpacity>
          )}

          {/* Botão Principal */}
          <TouchableOpacity
            style={[styles.primaryButton, loading && styles.buttonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <LoadingSpinner size="small" color="white" />
            ) : (
              <Text style={styles.primaryButtonText}>Entrar</Text>
            )}
          </TouchableOpacity>

          {/* Botão Biometria - Só aparece após login com senha */}
          {biometricAvailable && biometricEnabled && showBiometricButton && (
            <TouchableOpacity
              style={[styles.biometricButton, loading && styles.buttonDisabled]}
              onPress={handleBiometricLogin}
              disabled={loading}
            >
              {loading ? (
                <LoadingSpinner size="small" color="white" />
              ) : (
                <>
                  <Text style={styles.biometricIcon}>🔐</Text>
                  <Text style={styles.biometricText}>Entrar com Biometria</Text>
                </>
              )}
            </TouchableOpacity>
          )}



          {/* Link Esqueci Senha */}
          <TouchableOpacity
            style={styles.forgotPassword}
            onPress={() => navigation.navigate('EsqueceuSenha' as any)}
          >
            <Text style={styles.forgotPasswordText}>
              Esqueci minha senha
            </Text>
          </TouchableOpacity>
        </View>


        {/* Debug Token Button (apenas para desenvolvimento) 
        {__DEV__ && (
          <TouchableOpacity
            style={styles.debugButton}
            onPress={() => setShowTokenDebug(true)}
          >
            <Text style={styles.debugButtonText}>🔍 Debug Token</Text>
          </TouchableOpacit y>
        )} */}

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Precisa de ajuda? Entre em contato!          </Text>

          {/* Botão WhatsApp */}
          <TouchableOpacity style={styles.whatsappButton} onPress={abrirWhatsApp}>
            <Text style={styles.whatsappIcon}>💬</Text>
            <Text style={styles.whatsappText}>(51) 3287-4300</Text>
          </TouchableOpacity>
        </View>
      </ResponsiveScrollView>

      {/* Token Debug Info Modal */}
      <TokenDebugInfo
        visible={showTokenDebug}
        onClose={() => setShowTokenDebug(false)}
      />
    </KeyboardAvoidingView>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: getResponsivePadding(),
    paddingBottom: getResponsiveSpacing(30),
  },
  header: {
    paddingTop: getResponsiveSpacing(15),
    paddingBottom: getResponsiveSpacing(30),
    alignItems: 'center',
  },
  appTitle: {
    fontSize: getResponsiveTitleSize(28),
    fontWeight: '700',
    color: Cores.textoEscuro,
    textAlign: 'center',
    marginBottom: 4,
  },
  appSubtitle: {
    fontSize: getResponsiveTextSize(14),
    color: Cores.textoMedio,
    textAlign: 'center',
    fontWeight: '400',
  },
  loginCard: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 20,
    padding: isSmallScreen() ? 20 : 30,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(30),
  },
  welcomeTitle: {
    fontSize: getResponsiveTitleSize(24),
    fontWeight: '600',
    color: Cores.textoEscuro,
    marginBottom: 6,
  },
  welcomeSubtitle: {
    fontSize: getResponsiveTextSize(16),
    color: Cores.textoMedio,
    textAlign: 'center',
  },
  inputSection: {
    marginBottom: getResponsiveSpacing(20),
  },
  inputLabel: {
    fontSize: getResponsiveTextSize(14),
    fontWeight: '500',
    color: Cores.textoEscuro,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Cores.fundoCard,
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: Cores.bordaClara,
    paddingHorizontal: isSmallScreen() ? 12 : 16,
    minHeight: getResponsiveButtonHeight(),
  },
  input: {
    flex: 1,
    fontSize: getResponsiveTextSize(16),
    color: Cores.textoEscuro,
    paddingVertical: 12,
  },
  inputToggle: {
    padding: 8,
    marginLeft: 8,
    borderLeftWidth: 1,
    borderLeftColor: Cores.bordaClara,
  },
  inputToggleIcon: {
    fontSize: 20,
  },
  inputHelper: {
    fontSize: 12,
    color: Cores.textoClaro,
    marginTop: 6,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  rememberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 25,
    marginTop: 5,
  },
  rememberCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Cores.bordaMedia,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rememberCheckboxActive: {
    backgroundColor: Cores.primaria,
    borderColor: Cores.primaria,
  },
  rememberCheckmark: {
    color: Cores.textoBranco,
    fontSize: 12,
    fontWeight: 'bold',
  },
  rememberLabel: {
    fontSize: getResponsiveTextSize(14),
    color: Cores.textoMedio,
    fontWeight: '400',
  },
  primaryButton: {
    backgroundColor: Cores.primaria,
    borderRadius: 12,
    paddingVertical: isSmallScreen() ? 14 : 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    minHeight: getResponsiveButtonHeight(),
    shadowColor: Cores.primaria,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    color: Cores.textoBranco,
    fontSize: getResponsiveTextSize(18),
    fontWeight: '600',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  biometricButton: {
    backgroundColor: Cores.secundaria,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginBottom: 15,
    shadowColor: Cores.secundaria,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
  },
  biometricIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  biometricText: {
    color: Cores.textoBranco,
    fontSize: 16,
    fontWeight: '500',
  },
  biometricInfo: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginBottom: 15,
    borderWidth: 1,
    borderColor: 'rgba(33, 150, 243, 0.2)',
  },
  biometricInfoIcon: {
    fontSize: 16,
    marginRight: 8,
    opacity: 0.7,
  },
  biometricInfoText: {
    color: Cores.info,
    fontSize: 13,
    fontWeight: '400',
    textAlign: 'center',
    flex: 1,
  },
  forgotPassword: {
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 10,
  },
  forgotPasswordText: {
    color: Cores.secundaria,
    fontSize: 20,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  footer: {
    width: '100%',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 10, // espaço nas laterais
  },
  footerText: {
    color: Cores.secundaria,
    fontSize: 14,
    lineHeight: 18,
  },
  debugButton: {
    backgroundColor: '#FF5722',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginVertical: 10,
    marginHorizontal: 20,
  },
  debugButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Estilos do botão WhatsApp
  whatsappButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#25D366', // Cor oficial do WhatsApp
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    marginTop: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  whatsappIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  whatsappText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});