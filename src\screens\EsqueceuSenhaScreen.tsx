import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Cores from '../constants/Cores';
import EstilosComuns from '../constants/Estilos';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import { esqueceuSenha } from '../services/api';

interface EsqueceuSenhaScreenProps {
  navigation: {
    navigate: (screen: 'Login' | 'AlterarSenha', params?: any) => void;
    goBack: () => void;
  };
}

const EsqueceuSenhaScreen = ({ navigation }: EsqueceuSenhaScreenProps) => {
  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack());

  const [cardOrEmail, setCardOrEmail] = useState('');
  const [cpf, setCpf] = useState('');
  const [loading, setLoading] = useState(false);
  const [isEmailMode, setIsEmailMode] = useState(false); // false = cartão, true = email

  // Formatar cartão com pontos
  const formatCardNumber = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    const limited = cleaned.substring(0, 16);
    const formatted = limited.replace(/(\d{4})(?=\d)/g, '$1.');
    return formatted;
  };

  // Formatar CPF
  const formatCPF = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    const limited = cleaned.substring(0, 11);
    const formatted = limited.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    return formatted;
  };

  // Validar campos
  const validateFields = () => {
    if (!cardOrEmail.trim()) {
      Alert.alert('Erro', 'Por favor, informe o cartão ou email.');
      return false;
    }
    if (!cpf.trim()) {
      Alert.alert('Erro', 'Por favor, informe o CPF.');
      return false;
    }
    if (cpf.replace(/\D/g, '').length !== 11) {
      Alert.alert('Erro', 'CPF deve ter 11 dígitos.');
      return false;
    }
    return true;
  };

  // Enviar solicitação de recuperação
  const handleEsqueceuSenha = async () => {
    if (!validateFields()) return;

    setLoading(true);
    try {
      // Preparar dados para API
      const cartaoEmailLimpo = isEmailMode
        ? cardOrEmail // Email: manter como está (com pontos e @)
        : cardOrEmail.replace(/\./g, ''); // Cartão: remover pontos da formatação
      const cpfLimpo = cpf.replace(/\D/g, ''); // Remove formatação do CPF
      const selecionado = isEmailMode ? 'email' : 'cartao';

      console.log('📤 Enviando esqueceu senha:', {
        cartaoEmail: cartaoEmailLimpo,
        cpf: cpfLimpo,
        selecionado
      });

      // Chamar API de esqueceu senha
      const response = await esqueceuSenha(cartaoEmailLimpo, cpfLimpo, selecionado);

      if (response.success) {
        // Verificar se o email já estava confirmado (coderro = 0)
        if (response.data && response.data.userData) {
          // Email já confirmado - ir direto para alterar senha
          Alert.alert(
            'Email Já Confirmado',
            'Seu email já foi confirmado. Você pode alterar sua senha agora.',
            [{
              text: 'OK',
              onPress: () => {
                navigation.navigate('AlterarSenha', {
                  fromEmail: true,
                  emailConfirmed: true,
                  userData: response.data.userData,
                  dadosUsuario: response.data, // Dados completos igual ao Cordova
                  senhaInicial: true, // Igual ao Cordova: $.localStorage.set('senha_inicial', true)
                  hideMenu: true // Esconder menu quando email já confirmado
                });
              }
            }]
          );
        } else {
          // Email enviado para confirmação - voltar para login
          Alert.alert(
            'Email Enviado',
            response.message,
            [{
              text: 'OK',
              onPress: () => navigation.navigate('Login')
            }]
          );
        }
      } else {
        // Erro - mostrar mensagem e voltar para login
        Alert.alert(
          'Erro',
          response.message,
          [{
            text: 'OK',
            onPress: () => navigation.navigate('Login')
          }]
        );
      }
    } catch (error) {
      console.error('❌ Erro ao enviar esqueceu senha:', error);
      Alert.alert('Erro', 'Erro ao processar solicitação. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Recuperar Senha</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
      >
        <Text style={styles.title}>Esqueceu sua senha?</Text>
        <Text style={styles.subtitle}>
          Informe seus dados para receber instruções de recuperação
        </Text>

        {/* Campo Cartão/Email */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{isEmailMode ? 'Email' : 'Cartão'}</Text>
          <View style={styles.inputWithIcon}>
            <TextInput
              style={styles.inputField}
              placeholder={isEmailMode ? 'Digite seu email' : 'Digite o número do cartão'}
              value={cardOrEmail}
              onChangeText={(text) => {
                if (isEmailMode) {
                  setCardOrEmail(text); // Email sem formatação
                } else {
                  setCardOrEmail(formatCardNumber(text)); // Cartão com formatação
                }
              }}
              keyboardType={isEmailMode ? 'email-address' : 'numeric'}
              placeholderTextColor="#999"
            />
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => {
                setIsEmailMode(!isEmailMode);
                setCardOrEmail(''); // Limpar campo ao trocar modo
              }}
            >
              <Text style={styles.toggleIcon}>
                {isEmailMode ? '💳' : '📧'}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.helperText}>
            {isEmailMode ? 'Toque no ícone 💳 para usar cartão' : 'Toque no ícone 📧 para usar email'}
          </Text>
        </View>

        {/* Campo CPF */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>CPF</Text>
          <TextInput
            style={styles.input}
            placeholder="000.000.000-00"
            value={cpf}
            onChangeText={(text) => setCpf(formatCPF(text))}
            keyboardType="numeric"
            placeholderTextColor="#999"
          />
        </View>

        {/* Botão Enviar */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleEsqueceuSenha}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#FFF" size="small" />
          ) : (
            <Text style={styles.submitButtonText}>Enviar Solicitação</Text>
          )}
        </TouchableOpacity>

        {/* Link para voltar */}
        <TouchableOpacity
          style={styles.backToLoginButton}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={styles.backToLoginText}>Voltar para Login</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: 40,
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 20,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
    minHeight: 400,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Cores.textoEscuro,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: Cores.bordaClara,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Cores.bordaClara,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  inputField: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
  },
  toggleButton: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderLeftWidth: 1,
    borderLeftColor: Cores.bordaClara,
  },
  toggleIcon: {
    fontSize: 20,
  },
  helperText: {
    fontSize: 12,
    color: Cores.textoMedio,
    marginTop: 5,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  submitButton: {
    backgroundColor: Cores.primaria,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  submitButtonDisabled: {
    backgroundColor: Cores.bordaMedia,
  },
  submitButtonText: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  backToLoginButton: {
    marginTop: 20,
    alignItems: 'center',
  },
  backToLoginText: {
    color: Cores.primaria,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EsqueceuSenhaScreen;
