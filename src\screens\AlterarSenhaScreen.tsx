import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import Cores from '../constants/Cores';
import EstilosComuns from '../constants/Estilos';
import { verificarForcaSenha, alterarSenhaAssociado } from '../services/api';
import { useAppContext } from '../context/AppContext';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import PasswordStrengthBar from '../components/PasswordStrengthBar';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface AlterarSenhaScreenProps {
  navigation: {
    navigate: (screen: 'Login' | 'Home') => void;
    goBack: () => void;
  };
  route?: {
    params?: {
      fromEmail?: boolean; // Se veio do link do email
      fromLogin?: boolean; // Se veio do login (senha padrão)
      token?: string; // Token de validação do email
      emailConfirmed?: boolean; // Se o email foi confirmado
      userData?: {
        codass: string;
        codent: string;
        emaass: string;
        emailCadastro: string;
      };
      dadosUsuario?: any; // Dados completos do usuário
      senhaInicial?: boolean; // Se é senha inicial
      hideMenu?: boolean; // Se deve esconder o menu/header
    };
  };
}

const AlterarSenhaScreen = ({ navigation, route }: AlterarSenhaScreenProps) => {
  const [novaSenha, setNovaSenha] = useState('');
  const [confirmarSenha, setConfirmarSenha] = useState('');

  // Responsividade
  const screenHeight = Dimensions.get('window').height;
  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenHeight < 700 || screenWidth < 350;
  const [loading, setLoading] = useState(false);
  const [mostrarSenha, setMostrarSenha] = useState(false);
  const [forcaSenha, setForcaSenha] = useState<{
    erro: string;
    avaliacao: string;
    complemento: string;
  } | null>(null);

  const fromEmail = route?.params?.fromEmail || false;
  const fromLogin = route?.params?.fromLogin || false;
  const hideMenu = route?.params?.hideMenu || fromEmail || fromLogin; // Esconder menu se veio do email, login ou parâmetro específico
  const { getUsuarioData } = useAppContext();

  // Controle de navegação com botão voltar (sempre ativo)
  useCustomBackNavigation(() => {
    if (fromEmail || fromLogin) {
      // Se veio do email ou login, ir para Login
      navigation.navigate('Login');
    } else {
      // Se veio da navegação normal, voltar para Home
      navigation.navigate('Home');
    }
  });

  // Verificar força da senha em tempo real
  useEffect(() => {
    if (novaSenha.length >= 6) {
      verificarForcaDaSenha();
    } else {
      setForcaSenha(null);
    }
  }, [novaSenha]);

  const verificarForcaDaSenha = async () => {
    try {
      console.log('🔍 Verificando força da senha:', novaSenha);
      const response = await verificarForcaSenha(novaSenha);
      console.log('📦 Resposta verificação força:', response);

      if (response.success && response.data) {
        setForcaSenha({
          erro: response.data.erro?.toString() || '1',
          avaliacao: response.data.avaliacao || 'FRACA',
          complemento: response.data.complemento || 'Senha inválida'
        });
      } else {
        console.error('❌ Erro na verificação:', response.message);
        setForcaSenha({
          erro: '1',
          avaliacao: 'FRACA',
          complemento: 'Erro ao verificar senha'
        });
      }
    } catch (error) {
      console.error('❌ Erro ao verificar força da senha:', error);
      setForcaSenha({
        erro: '1',
        avaliacao: 'FRACA',
        complemento: 'Erro de conexão'
      });
    }
  };

  // Validar campos básicos (sem verificar força da senha)
  const validateBasicFields = () => {
    if (!novaSenha.trim()) {
      Alert.alert('Erro', 'Por favor, informe a nova senha.');
      return false;
    }

    // NOVA VALIDAÇÃO: Exatamente 6 dígitos numéricos
    if (novaSenha.length !== 6) {
      Alert.alert('Erro', 'A senha deve ter exatamente 6 dígitos.');
      return false;
    }

    // Verificar se contém apenas números
    if (!/^\d{6}$/.test(novaSenha)) {
      Alert.alert('Erro', 'A senha deve conter apenas números (6 dígitos).');
      return false;
    }

    if (novaSenha !== confirmarSenha) {
      Alert.alert('Erro', 'As senhas não coincidem.');
      return false;
    }

    return true;
  };

  // Validar campos incluindo força da senha
  const validateFields = () => {
    if (!validateBasicFields()) return false;

    if (forcaSenha && (forcaSenha.erro !== '0' && forcaSenha.avaliacao === 'FRACA')) {
      Alert.alert('Senha Fraca', `${forcaSenha.complemento}\n\nDeseja continuar mesmo assim?`, [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Continuar', onPress: () => proceedWithPasswordChange() }
      ]);
      return false;
    }

    return true;
  };

  // Prosseguir com a alteração da senha (sem validar força novamente)
  const proceedWithPasswordChange = async () => {
    if (!validateBasicFields()) return;
    await handleAlterarSenha();
  };

  // Alterar senha (sem validação adicional - já foi validado)
  const handleAlterarSenha = async () => {

    setLoading(true);
    try {
      let codass: string;
      let codent: string;

      // Verificar se veio do esqueceu senha ou login (tem dados do usuário nos parâmetros)
      if ((fromEmail || fromLogin) && route?.params?.userData) {
        // Usar dados do esqueceu senha, login ou deep link (todos vêm via userData)
        codass = route.params.userData.codass.toString();
        codent = route.params.userData.codent.toString();
        console.log(`📧 Usando dados do ${fromLogin ? 'login' : 'deep link/esqueceu senha'}:`, { codass, codent });
      } else {
        // Obter dados do usuário do contexto (login normal)
        const usuarioData = getUsuarioData();

        if (!usuarioData) {
          Alert.alert('Erro', 'Dados do usuário não encontrados. Faça login novamente.');
          navigation.navigate('Login');
          return;
        }

        codass = usuarioData.usuario.codass.toString();
        codent = usuarioData.usuario.codent.toString();
        console.log('🔐 Usando dados do contexto:', { codass, codent });
      }

      console.log('📤 Alterando senha:', {
        codass,
        codent,
        fromEmail,
        token: route?.params?.token
      });

      // Chamar API de alteração de senha
      const response = await alterarSenhaAssociado(
        codass,
        codent,
        novaSenha,
        undefined, // mail - não alteramos aqui
        undefined, // notification - não alteramos aqui
        !fromEmail // validateEmail - true se não veio do email
      );

      if (response.success) {
        // Atualizar senha em todos os storages para recarregamento automático
        try {
          // 1. Atualizar na variável global
          if ((globalThis as any).globalStorage) {
            (globalThis as any).globalStorage['senha_login'] = novaSenha;
            console.log('🔄 Senha global atualizada após alteração');
          }

          // 2. Atualizar no robustStorage (usado pelo HomeScreen para recarregamento)
          const { robustStorage } = require('../utils/robustStorage');
          await robustStorage.setItem('senha_login', novaSenha);
          console.log('🔄 Senha robustStorage atualizada após alteração');

          // 3. Atualizar no persistentStorage se existir
          const { persistentStorage } = require('../utils/persistentStorage');
          await persistentStorage.setItem('senha_login', novaSenha);
          console.log('🔄 Senha persistentStorage atualizada após alteração');

        } catch (error) {
          console.warn('⚠️ Erro ao atualizar senha nos storages:', error);
        }

        Alert.alert(
          'Senha Alterada',
          'Senha alterada com sucesso!',
          [{
            text: 'Ok',
            onPress: () => {
              // Lógica de navegação após alterar senha
              if (fromEmail || fromLogin) {
                // Se veio do email ou login (senha inicial) - ir para Login
                navigation.navigate('Login');
              } else {
                // Se veio da navegação normal (usuário logado) - voltar para Home
                navigation.navigate('Home');
              }
            }
          }]
        );
      } else {
        Alert.alert('Erro', response.message);
      }
    } catch (error) {
      console.error('❌ Erro ao alterar senha:', error);
      Alert.alert('Erro', 'Erro ao alterar senha. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };



  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />

      {/* Header padrão - sempre mostrar */}
      <Header
        title={fromEmail ? 'Definir Nova Senha' : fromLogin ? 'Definir Senha Inicial' : 'Alterar Senha'}
        onBackPress={() => {
          if (fromEmail || fromLogin) {
            navigation.navigate('Login');
          } else {
            navigation.navigate('Home');
          }
        }}
        showBackButton={true}
      />

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >

      <ScrollView style={[styles.content, isSmallScreen && styles.contentSmall]} showsVerticalScrollIndicator={false}>
        {/* Título e descrição */}
        <View style={[styles.titleContainer, isSmallScreen && styles.titleContainerSmall]}>
          <Text style={[styles.title, isSmallScreen && styles.titleSmall]}>
            {fromEmail ? 'Defina sua nova senha' : fromLogin ? 'Defina sua senha inicial' : 'Altere sua senha'}
          </Text>
          <Text style={[styles.subtitle, isSmallScreen && styles.subtitleSmall]}>
            {fromEmail
              ? 'Crie uma senha segura para acessar sua conta'
              : fromLogin
              ? 'Defina uma senha segura para seu primeiro acesso'
              : 'Digite sua nova senha abaixo'
            }
          </Text>
        </View>

        {/* Campo Nova Senha */}
        <View style={[styles.inputContainer, isSmallScreen && styles.inputContainerSmall]}>
          <Text style={[styles.inputLabel, isSmallScreen && styles.inputLabelSmall]}>Nova Senha</Text>
          <View style={styles.passwordContainer}>
            <TextInput
              style={styles.passwordInput}
              value={novaSenha}
              onChangeText={(text) => {
                // Permitir apenas números e limitar a 6 dígitos
                const numericText = text.replace(/[^0-9]/g, '').substring(0, 6);
                setNovaSenha(numericText);
              }}
              placeholder="Nova senha"
              secureTextEntry={!mostrarSenha}
              placeholderTextColor="#999"
              keyboardType="numeric"
              maxLength={6}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setMostrarSenha(!mostrarSenha)}
            >
              <Text style={styles.eyeText}>{mostrarSenha ? '🚫' : '👁'}</Text>
            </TouchableOpacity>
          </View>
          
          {/* Barra de força da senha */}
          <PasswordStrengthBar strength={forcaSenha} />
        </View>

        {/* Campo Confirmar Senha */}
        <View style={[styles.inputContainer, isSmallScreen && styles.inputContainerSmall]}>
          <Text style={[styles.inputLabel, isSmallScreen && styles.inputLabelSmall]}>Confirmar Senha</Text>
          <TextInput
            style={styles.input}
            value={confirmarSenha}
            onChangeText={setConfirmarSenha}
            placeholder="Confirme nova senha"
            secureTextEntry={true}
            placeholderTextColor="#999"
            autoCapitalize="none"
            keyboardType="numeric"
            maxLength={6}
          />
        </View>

        {/* Botão Alterar */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={() => {
            if (validateFields()) {
              handleAlterarSenha();
            }
          }}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#FFF" size="small" />
          ) : (
            <Text style={styles.submitButtonText}>
              {fromEmail ? 'Definir Senha' : 'Alterar Senha'}
            </Text>
          )}
        </TouchableOpacity>

        {/* Link para voltar - só mostrar se não esconder menu e não for do email */}
        {!hideMenu && !fromEmail && (
          <TouchableOpacity
            style={styles.backToHomeButton}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.backToHomeText}>Voltar ao Início</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
      </KeyboardAvoidingView>

      {/* Footer - só mostrar se não veio do email ou login (deep link) */}
      {!fromEmail && !fromLogin && (
        <Footer currentScreen="Home" navigation={navigation} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
    paddingHorizontal: 25,
    paddingTop: 30,
  },
  contentSmall: {
    paddingHorizontal: 20,
    paddingTop: 15,
  },
  titleContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  titleContainerSmall: {
    marginBottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 10,
    textAlign: 'center',
  },
  titleSmall: {
    fontSize: 20,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
    lineHeight: 22,
  },
  subtitleSmall: {
    fontSize: 14,
    lineHeight: 18,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputContainerSmall: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Cores.textoEscuro,
    marginBottom: 8,
  },
  inputLabelSmall: {
    fontSize: 14,
    marginBottom: 6,
  },
  input: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: Cores.textoEscuro,
    borderWidth: 1,
    borderColor: Cores.bordaClara,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Cores.bordaClara,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: Cores.textoEscuro,
  },
  eyeButton: {
    padding: 12,
  },
    eyeText: {
    fontSize: 18,
  },
  eyeIcon: {
    fontSize: 20,
  },

  submitButton: {
    backgroundColor: Cores.primaria,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  submitButtonDisabled: {
    backgroundColor: Cores.bordaMedia,
  },
  submitButtonText: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  backToHomeButton: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  backToHomeText: {
    color: Cores.primaria,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AlterarSenhaScreen;
