import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import pushNotificationService from '../services/pushNotificationService';
import { nativeStorage } from '../utils/nativeStorage';
import Cores from '../constants/Cores';

interface DebugInfo {
  timestamp: string;
  environment: string;
  executionEnv: string;
  isDevice: boolean;
  token: string | null;
  tokenType: string | null;
  isReal: boolean;
  projectId: string | null;
  expoProjectId: string | null;
  firebaseProjectId: string;
  permissions: string;
  serviceInfo: any;
}

interface DebugLogsScreenProps {
  navigation: {
    goBack: () => void;
    navigate: (screen: string) => void;
  };
}

export default function DebugLogsScreen({ navigation }: DebugLogsScreenProps) {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  const loadDebugInfo = async () => {
    try {
      console.log('🔍 === DEBUG LOGS SCREEN CARREGANDO ===');
      
      // Capturar logs do console (simulado)
      const currentLogs = [
        `[${new Date().toLocaleTimeString()}] 🔍 Debug Logs Screen carregado`,
        `[${new Date().toLocaleTimeString()}] 📱 Dispositivo: ${Device.isDevice ? 'Físico' : 'Simulador'}`,
        `[${new Date().toLocaleTimeString()}] 🏭 Ambiente: ${Constants.executionEnvironment}`,
      ];

      // Obter informações detalhadas
      const currentToken = pushNotificationService.getToken();
      const storedToken = await nativeStorage.getItem('push_token');
      const tokenType = await nativeStorage.getItem('token_type');
      const isRealFlag = await nativeStorage.getItem('token_is_real');
      
      // Project IDs
      const expoProjectId = Constants.expoConfig?.extra?.eas?.projectId || 
                           Constants.expoConfig?.extra?.expoProjectId;
      
      const firebaseProjectId = 'tecbizappass'; // Do seu firebase config

      // Verificar permissões
      let permissionStatus = 'Desconhecido';
      try {
        const { status } = await Notifications.getPermissionsAsync();
        const hasPermission = status === 'granted';
        permissionStatus = hasPermission ? 'Concedida' : 'Negada';
      } catch (error) {
        permissionStatus = `Erro: ${error}`;
      }

      const finalToken = currentToken || storedToken;
      const isReal = finalToken ? finalToken.startsWith('ExponentPushToken[') : false;

      const info: DebugInfo = {
        timestamp: new Date().toLocaleString(),
        environment: Constants.executionEnvironment || 'unknown',
        executionEnv: Constants.executionEnvironment || 'unknown',
        isDevice: Device.isDevice,
        token: finalToken,
        tokenType: tokenType,
        isReal: isReal,
        projectId: expoProjectId,
        expoProjectId: expoProjectId,
        firebaseProjectId: firebaseProjectId,
        permissions: permissionStatus,
        serviceInfo: pushNotificationService.getServiceInfo()
      };

      // Adicionar logs específicos
      currentLogs.push(`[${new Date().toLocaleTimeString()}] 🎯 Token encontrado: ${finalToken ? 'SIM' : 'NÃO'}`);
      currentLogs.push(`[${new Date().toLocaleTimeString()}] 📊 Token é real: ${isReal ? 'SIM' : 'NÃO'}`);
      currentLogs.push(`[${new Date().toLocaleTimeString()}] 🆔 Expo Project ID: ${expoProjectId || 'NÃO ENCONTRADO'}`);
      currentLogs.push(`[${new Date().toLocaleTimeString()}] 🔑 Permissões: ${permissionStatus}`);

      setDebugInfo(info);
      setLogs(currentLogs);
      
      console.log('✅ Debug info carregada:', info);
      
    } catch (error) {
      console.error('❌ Erro ao carregar debug info:', error);
      Alert.alert('Erro', 'Falha ao carregar informações de debug');
    }
  };

  const testTokenGeneration = async () => {
    try {
      Alert.alert(
        'Testar Geração de Token',
        'Isso irá tentar gerar um novo token. Continuar?',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Sim',
            onPress: async () => {
              console.log('🔄 === TESTE DE GERAÇÃO DE TOKEN INICIADO ===');
              
              const newLogs = [...logs];
              newLogs.push(`[${new Date().toLocaleTimeString()}] 🔄 Iniciando teste de geração...`);
              setLogs(newLogs);

              const newToken = await pushNotificationService.initialize();
              
              if (newToken) {
                const isNewTokenReal = newToken.startsWith('ExponentPushToken[');
                newLogs.push(`[${new Date().toLocaleTimeString()}] ✅ Novo token gerado!`);
                newLogs.push(`[${new Date().toLocaleTimeString()}] 📊 Novo token é real: ${isNewTokenReal ? 'SIM' : 'NÃO'}`);
                newLogs.push(`[${new Date().toLocaleTimeString()}] 🎯 Token: ${newToken.substring(0, 50)}...`);
                
                Alert.alert(
                  'Token Gerado',
                  `Novo token: ${newToken.substring(0, 30)}...\n\nÉ real: ${isNewTokenReal ? 'SIM' : 'NÃO'}`
                );
              } else {
                newLogs.push(`[${new Date().toLocaleTimeString()}] ❌ Falha ao gerar token`);
                Alert.alert('Erro', 'Falha ao gerar novo token');
              }
              
              setLogs(newLogs);
              loadDebugInfo(); // Recarregar info
            }
          }
        ]
      );
    } catch (error) {
      console.error('❌ Erro no teste:', error);
      Alert.alert('Erro', 'Falha no teste de geração: ' + error);
    }
  };

  const copyDebugInfo = () => {
    if (debugInfo) {
      const debugText = `
=== DEBUG INFO TECBIZ ===
Timestamp: ${debugInfo.timestamp}
Ambiente: ${debugInfo.environment}
Dispositivo: ${debugInfo.isDevice ? 'Físico' : 'Simulador'}
Token: ${debugInfo.token || 'NENHUM'}
Token Real: ${debugInfo.isReal ? 'SIM' : 'NÃO'}
Expo Project ID: ${debugInfo.projectId || 'NÃO ENCONTRADO'}
Firebase Project ID: ${debugInfo.firebaseProjectId}
Permissões: ${debugInfo.permissions}

=== LOGS ===
${logs.join('\n')}
      `;
      
      Alert.alert(
        'Debug Info Copiada',
        debugText,
        [{ text: 'OK' }]
      );
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadDebugInfo().finally(() => setRefreshing(false));
  };

  useEffect(() => {
    loadDebugInfo();
  }, []);

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>← Voltar</Text>
        </TouchableOpacity>
        <Text style={styles.title}>🔍 Debug Logs</Text>
        <Text style={styles.subtitle}>Logs em Tempo Real</Text>
      </View>

      {debugInfo ? (
        <View style={styles.content}>
          {/* Status Geral */}
          <View style={[styles.card, debugInfo.isReal ? styles.successCard : styles.warningCard]}>
            <Text style={styles.cardTitle}>
              {debugInfo.isReal ? '✅ TOKEN REAL DETECTADO' : '⚠️ TOKEN NÃO-REAL'}
            </Text>
            <Text style={styles.cardSubtitle}>
              {debugInfo.isReal ? 'Sistema funcionando corretamente' : 'Problema na geração do token'}
            </Text>
          </View>

          {/* Informações do Sistema */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>📱 Sistema</Text>
            <Text style={styles.infoText}>Ambiente: {debugInfo.environment}</Text>
            <Text style={styles.infoText}>Dispositivo: {debugInfo.isDevice ? 'Físico' : 'Simulador'}</Text>
            <Text style={styles.infoText}>Permissões: {debugInfo.permissions}</Text>
            <Text style={styles.infoText}>Timestamp: {debugInfo.timestamp}</Text>
          </View>

          {/* Informações do Token */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>🎯 Token</Text>
            <Text style={styles.tokenText} numberOfLines={3}>
              {debugInfo.token || 'NENHUM TOKEN ENCONTRADO'}
            </Text>
            <Text style={styles.infoText}>Tipo: {debugInfo.tokenType || 'Desconhecido'}</Text>
            <Text style={styles.infoText}>É Real: {debugInfo.isReal ? 'SIM' : 'NÃO'}</Text>
          </View>

          {/* Project IDs */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>🆔 Project IDs</Text>
            <Text style={styles.infoText}>Expo: {debugInfo.projectId || 'NÃO ENCONTRADO'}</Text>
            <Text style={styles.infoText}>Firebase: {debugInfo.firebaseProjectId}</Text>
          </View>

          {/* Logs */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>📋 Logs Recentes</Text>
            <ScrollView style={styles.logsContainer} nestedScrollEnabled>
              {logs.map((log, index) => (
                <Text key={index} style={styles.logText}>{log}</Text>
              ))}
            </ScrollView>
          </View>

          {/* Ações */}
          <View style={styles.actions}>
            <TouchableOpacity style={styles.button} onPress={testTokenGeneration}>
              <Text style={styles.buttonText}>🧪 Testar Geração</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={copyDebugInfo}>
              <Text style={styles.buttonText}>📋 Copiar Debug Info</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={handleRefresh}>
              <Text style={styles.buttonText}>🔄 Atualizar</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.loading}>
          <Text style={styles.loadingText}>Carregando debug info...</Text>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: Cores.primary,
    padding: 20,
    alignItems: 'center',
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 20,
    top: 20,
    zIndex: 1,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  content: {
    padding: 15,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  successCard: {
    borderLeftWidth: 5,
    borderLeftColor: '#4CAF50',
  },
  warningCard: {
    borderLeftWidth: 5,
    borderLeftColor: '#FF9800',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  tokenText: {
    fontSize: 12,
    fontFamily: 'monospace',
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#555',
  },
  logsContainer: {
    maxHeight: 200,
    backgroundColor: '#f8f8f8',
    padding: 10,
    borderRadius: 5,
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
    color: '#333',
  },
  actions: {
    marginVertical: 10,
  },
  button: {
    backgroundColor: Cores.primary,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 50,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
});
