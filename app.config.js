import pkg from './package.json';

export default {
  expo: {
    name: "TecBiz Associado",
    slug: "TecBizExpoApp",
    version: pkg.version, // ← Pega do package.json
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,

    splash: {
      image: "./assets/icon.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },


    android: {
      package: "com.tecbiz.tecbizassociadospush",
      googleServicesFile: "./google-services.json",
      adaptiveIcon: {
        foregroundImage: "./assets/icon-foreground.png",
        backgroundColor: "#FFFFFF",
      },

      // Configurações de SDK para compatibilidade com projeto Cordova anterior
      minSdkVersion: 26,
      compileSdkVersion: 35,
      targetSdkVersion: 35,

      // Configurações para melhor compatibilidade de dispositivos
      allowBackup: true,

      // ✅ Suporte melhorado para diferentes densidades e tamanhos de tela
      resizeableActivity: true,
      supportsRtl: true,

      // ✅ Configurações para dispositivos de tela grande (tablets, foldables, etc.)
      largeScreenSupport: true,

      // ✅ Remover restrições de orientação para melhor compatibilidade
      // Permitir que o app funcione em diferentes orientações quando necessário
      screenOrientation: "portrait", // Padrão portrait, mas sem restrições rígidas

      // ✅ Configurações de manifesto para melhor compatibilidade
      manifestPlaceholders: {
        // Suporte a diferentes configurações de tela
        "android:resizeableActivity": "true",
        "android:supportsPictureInPicture": "false",
        // Configurações para dispositivos foldable
        "android:maxAspectRatio": "2.4",
        "android:minAspectRatio": "1.33"
      },

      permissions: [
        "NOTIFICATIONS",
        "RECEIVE_BOOT_COMPLETED",
        "VIBRATE",
        "WAKE_LOCK",
        "com.google.android.c2dm.permission.RECEIVE",
        "com.google.android.gms.permission.AD_ID"
      ],

      // ✅ Configurações de navegação e status bar atualizadas
      navigationBar: {
        backgroundColor: '#FFB805',
        barStyle: "light-content"
      },
      statusBar: {
        backgroundColor: '#FFB805',
        barStyle: "light-content",
        translucent: false
      }
    },

    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.tecbiz.tecbizassociadospush",

      // ✅ Melhor suporte para diferentes tamanhos de tela no iOS
      requireFullScreen: false, // Permitir uso em multitasking

      // Configurações de status bar para iOS
      statusBar: {
        backgroundColor: '#FFB805',
        barStyle: "light-content"
      }
    },

    // 🎯 Configurações globais de status bar
    statusBar: {
      backgroundColor: '#FFB805',
      style: "light"
    },

  web: {
    favicon: "./assets/favicon.png"
  },
  notification: {
    icon: "./assets/notification-icon.png",
    color: "#000000",
    androidMode: "default",
    androidCollapsedTitle: "TecBiz"
  },
  scheme: "tecbizapp",
  plugins: [
    [
      "expo-notifications",
      {
        icon: "./assets/notification-icon.png",
        color: "#ffffff",
        defaultChannel: "default"
      }
    ],
    "expo-secure-store",
    // ✅ Plugin para melhor suporte a diferentes tamanhos de tela
    [
      "expo-screen-orientation",
      {
        initialOrientation: "PORTRAIT"
      }
    ],
    // ✅ Plugin personalizado para resolver avisos do Google Play Console
    "./plugins/android-compatibility-plugin.js"
  ],
  extra: {
    eas: {
      projectId: "78c39496-1095-4079-8e21-a92c90832c74"
    },
    // Adicionar projectId também aqui para garantir acesso
    expoProjectId: "78c39496-1095-4079-8e21-a92c90832c74"
  },
  owner: "mauriciocdz07"
}
};