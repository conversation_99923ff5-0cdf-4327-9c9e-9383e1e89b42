// Configuração de rede para compatibilidade com Android 8.0
import { Platform } from 'react-native';
import { createCompatibleAbortController } from './polyfills';

// Configurações específicas para Android 8.0
export const ANDROID_8_CONFIG = {
  // Timeout mais longo para conexões em Android antigo
  timeout: 120000,
  // Headers adicionais para compatibilidade
  headers: {
    'User-Agent': 'TecBizApp/1.0.0 (Android)',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Cache-Control': 'no-cache',
  },
  // Configurações de retry
  retryConfig: {
    retries: 3,
    retryDelay: 1000,
    retryCondition: (error: any) => {
      // Retry em erros de rede ou timeout
      return error.code === 'NETWORK_ERROR' || 
             error.code === 'TIMEOUT' ||
             error.message?.includes('SSL') ||
             error.message?.includes('certificate');
    }
  }
};

/**
 * Verificar se é Android 8.0 ou inferior
 */
export const isAndroid8OrLower = (): boolean => {
  if (Platform.OS !== 'android') return false;
  
  try {
    const version = Platform.Version;
    return typeof version === 'number' && version <= 26; // API 26 = Android 8.0
  } catch (error) {
    console.warn('Erro ao verificar versão do Android:', error);
    return false;
  }
};

/**
 * Obter configurações de rede baseadas na versão do Android
 */
export const getNetworkConfig = () => {
  const baseConfig = {
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }
  };

  if (isAndroid8OrLower()) {
    console.log('📱 Aplicando configurações para Android 8.0 ou inferior');
    return {
      ...baseConfig,
      ...ANDROID_8_CONFIG,
      headers: {
        ...baseConfig.headers,
        ...ANDROID_8_CONFIG.headers,
      }
    };
  }

  return baseConfig;
};

/**
 * Wrapper para fetch com configurações específicas do Android 8.0
 * Versão simplificada para evitar loops infinitos
 */
export const compatibleFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  // Para Android 8.0, usar AbortController manual ao invés de AbortSignal.timeout
  if (isAndroid8OrLower()) {
    const controller = createCompatibleAbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 90000); // 90 segundos para Android 8.0

    // Para Android 8.0, ir direto para HTTP se a URL for HTTPS
    const urlsToTry = [];

    if (url.startsWith('https://')) {
      // Android 8.0 tem problemas com HTTPS - ir direto para HTTP
      console.log('🔄 Android 8.0 detectado - usando HTTP diretamente para melhor compatibilidade');
      urlsToTry.push(url.replace('https://', 'http://')); // HTTP primeiro
      urlsToTry.push(url); // HTTPS como fallback (caso o servidor não aceite HTTP)
    } else {
      urlsToTry.push(url); // Manter URL original
    }

    for (let urlIndex = 0; urlIndex < urlsToTry.length; urlIndex++) {
      const currentUrl = urlsToTry[urlIndex];
      const isHttpFirst = urlIndex === 0 && currentUrl.startsWith('http://') && url.startsWith('https://');
      const isHttpsFallback = urlIndex > 0 && currentUrl.startsWith('https://');

      if (isHttpFirst) {
        console.log('🚀 Android 8.0 - Usando HTTP diretamente (otimizado)');
      } else if (isHttpsFallback) {
        console.log('🔄 HTTP falhou, tentando HTTPS como fallback...');
      }

      // Configurações para tentar
      const configurations = [
        // Configuração 1: Headers básicos
        {
          ...options,
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Cache-Control': 'no-cache',
            ...options.headers,
          },
          signal: controller.signal,
        },
        // Configuração 2: Sem headers extras
        {
          ...options,
          signal: controller.signal,
        }
      ];

      for (let i = 0; i < configurations.length; i++) {
        const config = configurations[i];

        try {
          const protocol = currentUrl.startsWith('https://') ? 'HTTPS' : 'HTTP';
          const priority = (isHttpFirst && protocol === 'HTTP') ? '(OTIMIZADO)' :
                          (isHttpsFallback && protocol === 'HTTPS') ? '(FALLBACK)' : '';

          console.log(`🔗 Tentativa ${protocol} ${priority} ${i + 1}/${configurations.length} para:`, currentUrl);
          console.log('🔧 Headers enviados:', JSON.stringify(config.headers, null, 2));

          const response = await fetch(currentUrl, config);
          clearTimeout(timeoutId);

          console.log(`✅ Sucesso ${protocol} ${priority} - Status:`, response.status);
          return response;
        } catch (error: any) {
          const protocol = currentUrl.startsWith('https://') ? 'HTTPS' : 'HTTP';
          const priority = (isHttpFirst && protocol === 'HTTP') ? '(OTIMIZADO)' :
                          (isHttpsFallback && protocol === 'HTTPS') ? '(FALLBACK)' : '';

          console.error(`❌ Tentativa ${protocol} ${priority} ${i + 1} falhou:`, {
            message: error.message,
            name: error.name,
          });

          // Se não é a última configuração desta URL, continuar
          if (i < configurations.length - 1) {
            console.log(`🔄 Tentando próxima configuração...`);
            continue;
          }

          // Se não é a última URL, tentar próxima URL
          if (urlIndex < urlsToTry.length - 1) {
            console.log(`🔄 Tentando próximo protocolo...`);
            break; // Sair do loop de configurações para tentar próxima URL
          }

          // Última tentativa de tudo falhou
          clearTimeout(timeoutId);

          if (error.name === 'AbortError') {
            throw new Error('TIMEOUT');
          }
          throw error;
        }
      }
    }
  }

  // Para versões mais recentes, usar comportamento padrão
  return fetch(url, options);
};

/**
 * Configurar interceptadores globais para melhor compatibilidade
 * DESABILITADO - estava causando loop infinito com Metro bundler
 */
export const setupNetworkInterceptors = () => {
  if (isAndroid8OrLower()) {
    console.log('🔧 Interceptadores de rede desabilitados para evitar loop infinito');
    // Interceptador global removido para evitar conflito com Metro bundler
    // As correções de compatibilidade são aplicadas diretamente no compatibleFetch
  }
};

/**
 * Verificar conectividade de rede específica para Android 8.0
 */
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    await compatibleFetch('https://www.google.com', {
      method: 'HEAD',
      mode: 'no-cors',
    });

    return true;
  } catch (error) {
    console.warn('⚠️ Problema de conectividade detectado:', error);
    return false;
  }
};

/**
 * Configurações específicas para o domínio da TecBiz
 */
export const getTecBizNetworkConfig = () => {
  const baseConfig = getNetworkConfig();

  // Configurações específicas para Android 8.0
  if (isAndroid8OrLower()) {
    return {
      ...baseConfig,
      headers: {
        ...baseConfig.headers,
        // Headers simplificados para Android 8.0
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'pt-BR,pt;q=0.9',
        'Cache-Control': 'no-cache',
      },
      // Remover credentials para Android 8.0 (pode causar problemas)
      mode: 'cors' as RequestMode,
    };
  }

  // Configurações para versões mais recentes
  return {
    ...baseConfig,
    headers: {
      ...baseConfig.headers,
      // Headers específicos para a API da TecBiz
      'X-Requested-With': 'XMLHttpRequest',
      'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
    },
    // Configurações específicas para HTTPS da TecBiz
    credentials: 'include' as RequestCredentials,
  };
};

/**
 * Inicializar configurações de rede
 */
export const initializeNetworkConfig = () => {
  console.log('🌐 Inicializando configurações de rede...');
  
  if (isAndroid8OrLower()) {
    console.log('📱 Detectado Android 8.0 ou inferior - aplicando configurações especiais');
    setupNetworkInterceptors();
  }
  
  console.log('✅ Configurações de rede inicializadas');
};

export default {
  isAndroid8OrLower,
  getNetworkConfig,
  compatibleFetch,
  setupNetworkInterceptors,
  checkNetworkConnectivity,
  getTecBizNetworkConfig,
  initializeNetworkConfig,
  ANDROID_8_CONFIG,
};
