{"name": "tecbizexpoapp", "version": "5.0.5", "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/config-plugins": "^10.1.2", "expo": "53.0.20", "expo-device": "~7.1.4", "expo-local-authentication": "^16.0.5", "expo-notifications": "~0.31.4", "expo-screen-orientation": "^8.1.7", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "firebase": "^12.0.0", "react": "19.0.0", "react-native": "0.79.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}