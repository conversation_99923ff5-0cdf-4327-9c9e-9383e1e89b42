import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
// import { Picker } from '@react-native-picker/picker';
import LoadingSpinner from '../components/LoadingSpinner';
import Cores from '../constants/Cores';
import Estilos from '../constants/Estilos';
import { confirmarAutorizacoes } from '../services/api';
import { useAppContext } from '../context/AppContext';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import { getSafeBottomPadding } from '../utils/safeArea';

interface AutorizacaoItem {
  autnumaut: string;
  data_compra_formatada: string;
  valor_formatado: string;
  lojnomfan: string;
  autnumpar: string;
}

interface AutorizacoesPendentesScreenProps {
  navigation: {
    navigate: (screen: 'Home' | 'Login', params?: any) => void;
  };
  route: {
    params: {
      codass: string;
      codent: string;
      autorizacoes: {
        extrato: AutorizacaoItem[];
      };
      userData: any;
    };
  };
}

const AutorizacoesPendentesScreen = ({ navigation, route }: AutorizacoesPendentesScreenProps) => {
  // Controle de navegação com botão voltar - vai para Login (não pode voltar para Home)
  useCustomBackNavigation(() => navigation.navigate('Login'));

  // Verificação de segurança para evitar erros
  if (!route.params) {
    console.error('❌ Parâmetros não encontrados, redirecionando para Login');
    navigation.navigate('Home', null);
    return null;
  }

  const { codass, codent, autorizacoes, userData } = route.params;

  // Verificação adicional para autorizacoes
  if (!autorizacoes || !autorizacoes.extrato) {
    console.error('❌ Dados de autorizações inválidos, redirecionando para Home');
    navigation.navigate('Home', userData || null);
    return null;
  }

  const [loading, setLoading] = useState(false);
  const [reconhecimentos, setReconhecimentos] = useState<{ [key: string]: string }>({});
  const [mostrarJustificativa, setMostrarJustificativa] = useState(false);
  const [justificativa, setJustificativa] = useState('');

  const maxJust = 10; // Mínimo de caracteres para justificativa
  const { setAutorizacoesAtualizadas } = useAppContext();

  // Safe area padding para botão
  const safeBottomPadding = getSafeBottomPadding(20);

  console.log('🔍 AutorizacoesPendentesScreen renderizada');
  console.log('🔍 Codass:', codass, 'Codent:', codent);
  console.log('🔍 Autorizações:', autorizacoes.extrato.length);

  // Extrair nome do usuário
  const nomeUsuario = userData?.find((item: any) => item.usuario)?.usuario?.portador || 'ASSOCIADO';

  // Inicializar todas as autorizações como "Sim" (padrão do Cordova)
  useEffect(() => {
    const reconhecimentosIniciais: { [key: string]: string } = {};
    autorizacoes.extrato.forEach((item: AutorizacaoItem) => {
      reconhecimentosIniciais[item.autnumaut] = 'Sim';
    });
    setReconhecimentos(reconhecimentosIniciais);
    console.log('✅ Reconhecimentos inicializados como "Sim":', reconhecimentosIniciais);
  }, [autorizacoes.extrato]);

  const handleReconhecimentoChange = (autnumaut: string, valor: string) => {
    const novosReconhecimentos = {
      ...reconhecimentos,
      [autnumaut]: valor
    };
    setReconhecimentos(novosReconhecimentos);

    // Verificar se há algum "Não" para mostrar justificativa (como no Cordova)
    const temNao = Object.values(novosReconhecimentos).some(v => v === 'Não');
    setMostrarJustificativa(temNao);

    if (!temNao) {
      setJustificativa(''); // Limpar justificativa se não há "Não"
    }

    console.log('📝 Reconhecimento alterado:', autnumaut, '=', valor);
    console.log('📝 Mostrar justificativa:', temNao);
  };

  const handleConfirmar = async () => {
    try {
      setLoading(true);
      console.log('📝 Confirmando reconhecimentos:', reconhecimentos);

      // Separar autorizações confirmadas e negadas
      const autssim: string[] = [];
      const autsnao: string[] = [];

      Object.entries(reconhecimentos).forEach(([autnumaut, resposta]) => {
        if (resposta === 'Sim') {
          autssim.push(autnumaut);
        } else if (resposta === 'Não') {
          autsnao.push(autnumaut);
        }
      });

      // Verificar se há autorizações negadas e validar justificativa (como no Cordova)
      if (autsnao.length > 0) {
        if (justificativa.length < maxJust) {
          Alert.alert(
            'Justificativa Necessária',
            `Ao NÃO reconhecer qualquer autorização, é necessária uma justificativa de pelo menos ${maxJust} caracteres!`
          );
          setLoading(false);
          return;
        }

        // Confirmar bloqueio do cartão (como no Cordova)
        const confirmarBloqueio = await new Promise<boolean>((resolve) => {
          Alert.alert(
            'Atenção',
            'Autorizações marcadas como NÃO, ocasionam o BLOQUEIO do CARTÃO que só poderá ser desbloqueado pela sua Entidade!\n\nConfirmar estas informações?',
            [
              { text: 'Cancelar', onPress: () => resolve(false) },
              { text: 'Confirmar', onPress: () => resolve(true) },
            ]
          );
        });

        if (!confirmarBloqueio) {
          setLoading(false);
          return;
        }
      }

      console.log('📤 Enviando confirmação:', {
        codass,
        codent,
        autssim: autssim.join(','),
        autsnao: autsnao.join(','),
        justificativa
      });

      const response = await confirmarAutorizacoes(
        codass,
        codent,
        autssim.join(','),
        autsnao.join(','),
        justificativa
      );

      if (response.success) {
        // Marcar que as autorizações foram atualizadas
        setAutorizacoesAtualizadas(true);

        Alert.alert(
          'Confirmado',
          response.message,
          [{
            text: 'OK',
            onPress: () => {
              console.log('🧭 Redirecionando para Home após confirmação');
              navigation.navigate('Home', userData);
            }
          }]
        );
      } else {
        Alert.alert('Erro', response.message);
      }
    } catch (error) {
      console.error('❌ Erro ao confirmar autorizações:', error);
      Alert.alert('Erro', 'Erro ao confirmar autorizações. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={Estilos.containerLoading}>
        <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />
        <LoadingSpinner message="Confirmando autorizações..." size="large" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <StatusBar backgroundColor="#FF8C00" barStyle="light-content" />

      {/* Header laranja */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Autorizações pendentes</Text>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Nome do usuário */}
        <View style={styles.nomeContainer}>
          <Text style={styles.nomeLabel}>Nome: </Text>
          <Text style={styles.nomeValue}>{nomeUsuario}</Text>
        </View>

        {/* Aviso */}
        <View style={styles.avisoContainer}>
          <Text style={styles.avisoTexto}>
            Caso não reconheça alguma destas transações, marque como{' '}
            <Text style={styles.naoText}>NÃO</Text> e justifique.
          </Text>
          <Text style={styles.bloqueioTexto}>
            Seu cartão será <Text style={styles.bloqueadoText}>BLOQUEADO</Text> de imediato!
          </Text>
        </View>

        {/* Lista de autorizações */}
        {autorizacoes.extrato.map((item) => (
          <View key={item.autnumaut} style={styles.transacaoContainer}>
            <View style={styles.transacaoHeader}>
              <Text style={styles.dataText}>Data: {item.data_compra_formatada}</Text>
              <Text style={styles.valorText}>R${item.valor_formatado}</Text>
            </View>

            {/* Parcela em linha separada */}
            <View style={styles.parcelaContainer}>
              <Text style={styles.parcelaText}>Parcela: {item.autnumpar}</Text>
            </View>
            
            <View style={styles.transacaoBody}>
              <Text style={styles.conveniada}>Conveniada:</Text>
              <Text style={styles.conveniada}>{item.lojnomfan}</Text>
              
              <View style={styles.reconhecoContainer}>
                <Text style={styles.reconhecoLabel}>Reconheço: </Text>
                <View style={styles.botoesContainer}>
                  <TouchableOpacity
                    style={[
                      styles.botaoOpcao,
                      reconhecimentos[item.autnumaut] === 'Sim' && styles.botaoOpcaoSelecionado
                    ]}
                    onPress={() => handleReconhecimentoChange(item.autnumaut, 'Sim')}
                  >
                    <Text style={[
                      styles.botaoOpcaoTexto,
                      reconhecimentos[item.autnumaut] === 'Sim' && styles.botaoOpcaoTextoSelecionado
                    ]}>
                      Sim
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.botaoOpcao,
                      reconhecimentos[item.autnumaut] === 'Não' && styles.botaoOpcaoSelecionado
                    ]}
                    onPress={() => handleReconhecimentoChange(item.autnumaut, 'Não')}
                  >
                    <Text style={[
                      styles.botaoOpcaoTexto,
                      reconhecimentos[item.autnumaut] === 'Não' && styles.botaoOpcaoTextoSelecionado
                    ]}>
                      Não
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        ))}

        {/* Campo de justificativa (só aparece se há "Não") */}
        {mostrarJustificativa && (
          <View style={styles.justificativaContainer}>
            <Text style={styles.justificativaLabel}>
              Justificativa (mínimo {maxJust} caracteres):
            </Text>
            <TextInput
              style={styles.justificativaInput}
              multiline
              numberOfLines={4}
              value={justificativa}
              onChangeText={setJustificativa}
              placeholder="Descreva o motivo por não reconhecer as transações..."
              placeholderTextColor="#999999"
            />
            <Text style={styles.justificativaContador}>
              {justificativa.length}/{maxJust} caracteres
            </Text>
          </View>
        )}

        {/* Botão Confirmar */}
        <TouchableOpacity
          style={[styles.confirmarButton, { marginBottom: safeBottomPadding }]}
          onPress={handleConfirmar}
          disabled={loading}
        >
          <Text style={styles.confirmarButtonText}>Confirmar</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#FF8C00',
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  nomeContainer: {
    flexDirection: 'row',
    marginTop: 20,
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  nomeLabel: {
    fontSize: 16,
    color: '#666666',
  },
  nomeValue: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  avisoContainer: {
    marginBottom: 20,
  },
  avisoTexto: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 5,
  },
  naoText: {
    fontWeight: 'bold',
    color: '#333333',
  },
  bloqueioTexto: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  bloqueadoText: {
    fontWeight: 'bold',
    color: '#FF0000',
  },
  transacaoContainer: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
  },
  transacaoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  dataText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  valorText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: 'bold',
  },
  parcelaContainer: {
    marginTop: 5,
    marginBottom: 5,
  },
  parcelaText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  transacaoBody: {
    marginTop: 5,
  },
  conveniada: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  reconhecoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  reconhecoLabel: {
    fontSize: 14,
    color: '#333333',
    marginRight: 10,
  },
  botoesContainer: {
    flexDirection: 'row',
    flex: 1,
    gap: 10,
  },
  botaoOpcao: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  botaoOpcaoSelecionado: {
    backgroundColor: '#FF8C00',
    borderColor: '#FF8C00',
  },
  botaoOpcaoTexto: {
    fontSize: 14,
    color: '#333333',
  },
  botaoOpcaoTextoSelecionado: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  confirmarButton: {
    backgroundColor: '#FF8C00',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  confirmarButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  justificativaContainer: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  justificativaLabel: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
    marginBottom: 10,
  },
  justificativaInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    padding: 10,
    fontSize: 14,
    color: '#333333',
    textAlignVertical: 'top',
    minHeight: 80,
    backgroundColor: '#FFFFFF',
  },
  justificativaContador: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 5,
  },
});

export default AutorizacoesPendentesScreen;
